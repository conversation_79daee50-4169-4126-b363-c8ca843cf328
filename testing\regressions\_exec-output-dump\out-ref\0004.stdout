=== AUTH CRAM-MD5 supported
=== AUTH CRAM-SHA1 supported
=== AUTH DIGEST-MD5 supported
=== AUTH NTLM supported
=== Basic AUTH supported
=== Date Manipulation supported
=== High Resolution Timing supported
=== IPv4/v6 Socket Transport supported
=== Legacy IPv4 Socket Transport supported
=== Legacy IPv4/v6 Socket Transport supported
=== Local Hostname Detection supported
=== MX Routing supported
=== Netrc Credentials supported
=== Pipe Transport supported
=== TLS supported
=== UNIX Socket Transport supported

App Info:
  X-Mailer = swaks v99999999.9 jetmore.org/john/code/swaks/
  Cmd Line = %SWAKS_COMMAND% --from '<EMAIL>' --to '<EMAIL>' --helo 'host1.nodns.test.swaks.net' --server 'ser.ver' --quit-after 'mail' --auth-user 'user' --auth-password 'pass' --tls --hide-send --dump --xclient-addr '***********' --xclient-optional --proxy-version '1' --proxy-family 'TCP4' --proxy-source '***********' --proxy-source-port '111' --proxy-dest '*******' --proxy-dest-port '26'

Output Info:
  show_time_lapse    = FALSE
  show_raw_text      = FALSE
  suppress_data      = FALSE
  protect_prompt     = FALSE
  no_hints_send      = FALSE
  no_hints_recv      = FALSE
  no_hints_info      = FALSE
  silent             = 0
  dump_mail          = FALSE
  hide_send          = TRUE
  hide_receive       = FALSE
  hide_informational = FALSE
  hide_all           = FALSE
  trans_fh_of        = STDOUT (GLOB(0xdeadbeef),GLOB(0xdeadbeef))
  trans_fh_ef        = STDERR (GLOB(0xdeadbeef),GLOB(0xdeadbeef))

Transport Info:
  type            = socket-inet
  inet protocol   = any
  server          = ser.ver
  port            = 25
  local interface = 
  local port      = 
  copy routing    = FALSE

Protocol Info:
  protocol        = esmtp
  helo            = host1.nodns.test.swaks.net
  from            = <EMAIL>
  to              = <EMAIL>
  force getpwuid  = FALSE
  quit after      = mail
  drop after      = 
  drop after send = 
  server_only     = TRUE
  timeout         = 30
  pipeline        = FALSE
  prdr            = FALSE

XCLIENT Info:
  xclient         = optional
  no_verify       = FALSE
  before starttls = FALSE
  strings         = XCLIENT ADDR=***********

PROXY Info:
  proxy       = yes
  version     = 1
  family      = TCP4
  source      = ***********
  source port = 111
  dest        = *******
  dest port   = 26
  protocol    = STREAM
  command     = PROXY

TLS / Encryption Info:
  tls                 = starttls (required)
  peer cert           = 
  peer chain          = 
  local cert          = 
  local key           = 
  local chain         = 
  local cipher list   = 
  ca path             = 
  sni string          = 
  verify server cert  = FALSE
  verify target host  = 
  available protocols = TLS_PROTOCOL_LIST
  requested protocols = 

Authentication Info:
  auth           = required
  username       = 'user'
  password       = 'pass'
  show plaintext = FALSE
  hide password  = FALSE
  allowed types  = CRAM-MD5, CRAM-SHA1, DIGEST-MD5, LOGIN, LOGIN-INITIAL, MSN, NTLM, PLAIN, SPA
  extras         = 
  type map       = CRAM-MD5 = CRAM-MD5
                   CRAM-SHA1 = CRAM-SHA1
                   DIGEST-MD5 = DIGEST-MD5
                   LOGIN = LOGIN, LOGIN-INITIAL
                   NTLM = NTLM, SPA, MSN
                   PLAIN = PLAIN

DATA Info:
  data = <<.
Date: Wed, 03 Nov 1999 11:24:29 -0500
To: <EMAIL>
From: <EMAIL>
Subject: test Wed, 03 Nov 1999 11:24:29 -0500
Message-Id: <19991103112429.047942@localhost>
X-Mailer: swaks v99999999.9 jetmore.org/john/code/swaks/

This is a test mailing


.
