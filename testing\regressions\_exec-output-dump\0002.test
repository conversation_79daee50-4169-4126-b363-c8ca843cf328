pre action: REMOVE_FILE %OUTDIR%/%TESTID%.stdout %OUTDIR%/%TESTID%.stderr
test action: CMD_CAPTURE %SWAKS% --dump support,app,output,transport,protocol,xclient,proxy,tls,auth,data --to <EMAIL> --server ser.ver --helo host1.nodns.test.swaks.net --from <EMAIL>

pre action: REMOVE_FILE %OUTDIR%/%TESTID%.stdout.2 %OUTDIR%/%TESTID%.stderr.2
test action: CMD_CAPTURE:2 %SWAKS% --dump all --to <EMAIL> --server ser.ver --helo host1.nodns.test.swaks.net --from <EMAIL>

test action: MUNGE file:%OUTDIR%/%TESTID%.stdout   "munge_general,'Cmd Line',%QUOTE_DOUBLE%--dump '[^']+'%QUOTE_DOUBLE%,'DUMP_OPT_REMOVED'"
test action: MUNGE file:%OUTDIR%/%TESTID%.stdout.2 "munge_general,'Cmd Line',%QUOTE_DOUBLE%--dump '[^']+'%QUOTE_DOUBLE%,'DUMP_OPT_REMOVED'"

test action: MUNGE file:%OUTDIR%/%TESTID%.stdout   munge_standard
test action: MUNGE file:%OUTDIR%/%TESTID%.stdout.2 munge_standard

test result: COMPARE_FILE %OUTDIR%/%TESTID%.stdout %OUTDIR%/%TESTID%.stdout.2
test result: COMPARE_FILE %OUTDIR%/%TESTID%.stderr %OUTDIR%/%TESTID%.stderr.2
