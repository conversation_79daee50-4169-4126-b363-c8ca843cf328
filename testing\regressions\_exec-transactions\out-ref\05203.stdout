=== Trying pipe to %TEST_SERVER% --silent --domain pipe   part-0201-intialize-tls.txt   part-0000-connect-standard.txt   ...
=== Connected to %TEST_SERVER% --silent --domain pipe   part-0201-intialize-tls.txt   part-0000-connect-standard.txt   .
=== TLS started with cipher VERSION:CIPHER:BITS
=== TLS client certificate not requested and not sent
=== TLS no client certificate set
=== TLS peer[0]   subject=[/C=US/ST=Indiana/O=Swaks Development (node.example.com, with-SAN)/CN=node.example.com/emailAddress=<EMAIL>]
===               commonName=[node.example.com], subjectAltName=[DNS:node.example.com] notAfter=[2033-09-11T14:50:10Z]
=== TLS peer certificate failed CA verification (unable to get local issuer certificate), failed host verification (no host string available to verify)
=== Dropping connection
