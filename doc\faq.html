<!DOCTYPE html>
<html lang="en">
<head>
    <title>Swaks - Swiss Army Knife for SMTP</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/skeleton/2.0.4/skeleton.min.css" /> <!-- https://cdnjs.com/libraries/skeleton -->
    <style type="text/css">
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif }
        #subheader { font-size: 120% }
        h5 { padding-top: 0 }
        a:link { color: blue }
        a:visited { color: blue }
        a:active { color: navy }
        code { font-size: 120% }
        pre, code {
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            font-size: 90%;
        }
        .container { max-width: 600px }
        .header { margin-top: 3% }
        .footer { margin-bottom: 3% }
    </style>
</head>
<body>

<div class="section header">
    <div class="container">
        <div class="row">
            <a name="swaks"><h1><b>Swaks</b> - Swiss Army Knife for SMTP</h1></a>
            <span id="subheader">A scriptable, transaction-oriented SMTP test tool</span>
        </div>
        <hr/>
    </div>
</div>

<div class="container">
    <div class="row">
        <h4>Occasionally Asked Questions</h4>
        <p>
            These are questions that I occasionally get asked or I see pop up in a Google query string.
        </p>
        <p>
            If you are trying to figure out something using Swaks, drop me a line at <a href="mailto:<EMAIL>"><EMAIL></a>.  Swaks is a low-traffic project and I enjoy making it better, whether it's docs or code or just giving a hint in an email.
        </p>
        <p><a href="./index.html">return to main page</a></p>
    </div>
    <hr/>

    <div class="row">
        <div class="two columns">
            <b>Table of Contents</b>
        </div>
        <div class="ten columns">
            <ol>
                <li><a href="#mult_recip">How do I use Swaks to send email to multiple recipients?</a>
                <li><a href="#adding_header">How do I add a header?</a>
                <li><a href="#install_macosx">How do I install on Mac OS X?</a>
                <li><a href="#install_windows">How do I install on Windows?</a>
                <li><a href="#send_html_email">How do I send HTML email?</a>
                <li><a href="#bcc">How do I send a BCC email?</a>
                <li><a href="#support_ipv6">Does Swaks support IPv6?</a>
            </ol>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b><a name="mult_recip">Multiple Recipients</a></b>
        </div>
        <div class="ten columns">
            <h5>How do I use Swaks to send email to multiple recipients?</h5>
            <p>
                Multiple recipients can be specified by passing them as a single argument to the "--to" option.
                Any of the following will result in mail being sent <NAME_EMAIL> and <EMAIL>:
            </p>
            <p>
                Command line:
                <pre><code>
swaks --to <EMAIL>,<EMAIL>
                </code></pre>
            </p>
            <p>
                Environment variable:
                <pre><code>
SWAKS_OPT_to='<EMAIL>,<EMAIL>'
swaks
                </code></pre>
            </p>
            <p>
                Config file:
                <pre><code>
echo "--to <EMAIL>,<EMAIL>" >> .swaksrc
swaks --config .swaksrc
                </code></pre>
            </pre>
            <p>
                The argument to the --to option is passed deep into the heart of Swaks with no real validation checking.
                This is done intentionally - because Swaks is meant to be a test tool, oddball values should be allowed.
                One side effect of this is that including a space after the comma will result in an email address that starts with a comma,
                which is probably not what you want.
            </p>
            <p>
                There is one additional consideration here.  Since Swaks is oriented around a single SMTP-session per invocation,
                it will only ever connect to a single server, even if multiple recipients are specified.
                In the example above, both emails will be delivered to the MX server for example.com
                (since, as documented, the mail routing for the last domain in the list is used).
                This may or may not work, depending on the configuration of email server you are testing.
            </p>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b><a name="adding_header">Adding Headers</a></b>
        </div>
        <div class="ten columns">
            <h5>How do I add a header?</h5>
            <p>Use the --add-header option:</p>
            <pre><code>
swaks --add-header "X-Test-Header: foo"
            </code></pre>
            <p>
                There are lots more examples in <a href="http://jetmore.org/john/code/swaks/latest/doc/ref.txt">the spec</a>,
                look for --header and --add-header for details and nuance about each.
                A quick rule of thumb though is that you want --header to overwrite a header that already exists in your test email,
                and you want --add-header to add a completely new header, even if that same header already exists in your test email.
            </p>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b><a name="install_macosx">Install on Mac OS X</a></b>
        </div>
        <div class="ten columns">
            <h5>How do I install on Mac OS X?</h5>
            <p>
                See <a href='installation.html'>installation page</a>.
            </p>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b><a name="install_windows">Install on Windows</a></b>
        </div>
        <div class="ten columns">
            <h5>How do I install on Windows?</h5>
            <p>
                See <a href='installation.html'>installation page</a>.
            </p>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b><a name="send_html_email">HTML Email</a></b>
        </div>
        <div class="ten columns">
            <h5>How do I send HTML email?</h5>
            <p>
                This really depends on what you want to do.  At its base, Swaks is agnostic about what it sends in its DATA section,
                so you can craft whatever email you want and use it as the DATA of the message using the --data option.
                This is the best route for testing, where you usually have a fixed set of test cases to run, or you want
                to run the same command many times.  If you don't know what a raw email looks like, send yourself an email to a Gmail account
                and select "Show Original".  The entire text file is the format that the --data option expects
                (though it will probably be more complicated and have more headers than you need)
            </p>
            <p>
                On the other hand, if you're trying to do something like use Swaks to send an email copy of a nightly HTML file,
                and you want the file to show up correctly in your MUA, Swaks does have a couple of helper options.
            </p>
            <p>
                First, if you want the HTML file to be an attachment that can be openable or savable from your MUA,
                you want the --attach option.  For instance, the following command will attach the file report.html.
                --attach-type is optional, but setting it will help your MUA know what to do with the attachment:
            </p>
            <pre><code>
swaks --attach-type text/html --attach @report.html
            </code></pre>
            <p>
                Another interpretation of this question is "How do I send email which has an HTML-encoded body".
                This means sending an email that your MUA will display as HTML.  The following should work.
            </p>
            <pre><code>
swaks --attach-type text/html --attach-body @report.html
            </code></pre>
            <p>
                See the documentation for --attach-body for more information.
            </p>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b><a name="bcc">BCC</a></b>
        </div>
        <div class="ten columns">
            <h5>How do I send a BCC email?</h5>
            <p>
                Swaks does not have the --bcc option so many people seem to be asking for lately.
                It may never get it, as it's not really true to Swaks' core functionality as a transaction tester, versus an MUA.
                That said, for those who really need this functionality, here's how to do it.
            </p>
            <p>First, the reason you don't need a special option to do this:</p>
            <ol style="padding-left:7%">
                <li>The envelope-recipients of an email and the contents of the To: and Cc: headers in an email are only related by convention.</li>
                <li>By default, Swaks places the envelope-recipients (specified by --to) into the To: header.</li>
                <li>However, as the user of Swaks, you have complete freedom to set the --to option and the To: header independently.</li>
                <li>Therefore, to "BCC" someone is the same as specifying them in the --to recipients, but specifying a To: header that does not include them.</li>
            </ol>
            <p>
                So, to send a <NAME_EMAIL> and <EMAIL>, but <NAME_EMAIL>
                in the To: header (or, put another way, <NAME_EMAIL>), the following would work:
            </p>
            <pre><code>
swaks --to <EMAIL>,<EMAIL> --header "To: <EMAIL>"
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b><a name="support_ipv6">IPv6 Support</a></b>
        </div>
        <div class="ten columns">
            <h5>Does Swaks support IPv6?</h5>
            <p>Yes, as of release 20120320.0!  Woo!</p>
        </div>
    </div>
</div> <!-- end of content container -->


<!-- TODO

These are all the remaining Q-strings from 2011 through 9/16

swaks%20config%20file
swaks%20download
swaks+send+attachment
configuration%20et%20implementation%20de%20swaks%20sur%20nagios
testing+clamav+through+swaks
swaks+timeout+after+30+seconds
gtube%20swaks
swaks%20examples
how+to+send+a+empty+message+in+swaks
swaks%20lmtp
swaks%20send%20eicar
***+Host+did+not+advertise+STARTTLS+swaks
swaks%20stress%20test
swaks%20not%20including%20%5Cr
swaks+how+to+debug+smtps+465
swaks+%22Host+did+not+advertise+authentication%22
swaks%20send%20file
swaks%20TLS%20is%20not%20a%20recognized%20auth%20type%2C%20skipping
swaks+Host+did+not+advertise+authentication


       1  swaks+IPv6
       1  swaks%20local-interface
       1  swaks+ssl+certificate
       1  swaks%20enable%20tls
       1  download+swaks+sco
       1  swaks+smtps
       1  changing+data+and+subject+swaks
       1  nagios+swaks+smtp
       1  swaks%20attachment%20name
       1  send+multiple+emails+swaks
       1  multiple+emails+with+single+SWAKS+command
       1  swaks%20multiple%20attachments
       1  blank+message+id+swaks
       1  swaks%20multiple%20mails%20one%20session
       1  swaks+Stress+Testing+Tool.
       1  swaks+No+acceptable+authentication+types+available
       1  sending%20multiple%20messages%20with%20SWAKS
       1  swaks+tls+accept+certificate
       1  No%20acceptable%20authentication%20types%20available%20swaks
       1  swaksrc+example
       1  tls+tsting+swaks+win32
       1  swaks%20force%20tls
       1  swaks+message-charset
       1  send%20a%20s%2Fmime%20mail%20with%20swaks
       1  swaks+transaction+latency

20120227: swaksrc%20configuration%20file%20example%20swaks
	- should include a swaksrc and a sh snippet showing basic config changes with docs
-->

</body>
</html>
