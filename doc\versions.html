<!DOCTYPE html>
<html lang="en">
<head>
    <title>Swaks - Swiss Army Knife for SMTP</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/skeleton/2.0.4/skeleton.min.css" /> <!-- https://cdnjs.com/libraries/skeleton -->
    <style type="text/css">
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif }
        #subheader { font-size: 120% }
        #rss { font-size: 70% }
        a:link { color: blue }
        a:visited { color: blue }
        a:active { color: navy }
        pre { overflow-y: scroll }
        code { font-size: 110% }
        pre, code {
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            font-size: 90%;
        }
        .container { max-width: 600px }
        .header { margin-top: 3% }
        .footer { margin-bottom: 3% }
    </style>
</head>
<body>

<div class="section header">
    <div class="container">
        <div class="row">
            <a name="swaks"><h1><b>Swaks</b> - Swiss Army Knife for SMTP</h1></a>
            <span id="subheader">A scriptable, transaction-oriented SMTP test tool</span>
        </div>
        <hr/>
    </div>
</div>

<div class="container">
    <div class="row">
        <h4>Versions</h4>
        <p>
          This page lists every official release of Swaks, from newest to oldest.  All releases include that version's Changes,
          and newer releases also include a change summary.
        </p>
        <p><a href="./index.html">return to main page</a></p>
    </div>
    <hr/>

    <div class="row">
        <div class="two columns">
            <b>20240103.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20240103.0.tar.gz">distribution</a>,
                <a href="./files/swaks-20240103.0/swaks">script only</a>,
                <a href="./files/swaks-20240103.0/doc/ref.txt">version docs</a> (<a href="https://github.com/jetmore/swaks/blob/v20240103.0/doc/base.pod">rendered</a>),
                <a href="https://www.jetmore.org/john/blog/2024/01/swaks-release-20240103-0-available/">announcement</a>
            </p>
            <p>
                <b>Change Summary:</b>
            </p>
            <pre><code>
New Features:
  * Added --cc and --bcc options
  * Numerous TLS debugging and verification improvements
    * Debug output contains whether a client cert was requested and whether
      one was sent
    * Add new options --tls-verify-ca and --tls-verify-host to differentiate
      between types of certificate verification (--tls-verify does both)
    * Add --tls-target option to allow setting of hostname to be used in
      hostname verification.  This is useful in some inet debugging situations
      and required to do hostname verification with --socket or --pipe
    * Add --tls-chain (#60, initial implementation by Wolfgang Karall-Ahlborn)
    * Add --tls-get-peer-chain option (analogous to --tls-get-peer-cert, #73)
    * Certificate debug now includes all client and peer certs, it a chain
      was used (#73)
    * Certificate debug now includes notAfter, commonName, and subjectAltName
Notable Changes:
  * --output-file, --output-file-stderr, and --output-file-stdout now truncate
    the specified file if it already exists
  * Documentation improvements
  * Extensive test harness improvements
  * Add new stop-point XCLIENT-HELO to address lack of specificity when
    mixing XCLIENT usage with the HELO stop-point
  * Add new stop-point PROXY
  * Use IO::Socket::IP by default.  Will still use IO::Socket::INET/INET6
    to cover transition, but this is deprecated and will be removed in the
    future (#43)
  * TLS session debug information is now printed even if we decide not to
    continue the session (eg for failed verification)
  * Previously-deprecated functionality to allow some options to be either
    a filename or a literal string has been removed.  Using the '@' sigil is
    now the only was to specify file contents
  * Previously-deprecated -g option removed
Notable Bugs Fixed:
  * TLS certificate verification did not always work.  It should now
            </code></pre>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20201016 Testing: Document and improve automated testing to make it easier
           for new users to run in new environments
* 20201018 Change script interpreter line to use /usr/bin/env for portability
* 20201018 Testing: Add --winnow to run-all.pl
* 20201030 Fix date generation by confirming environment's strftime supports
           %z format token.  Windows supports strftime but not %z.
* 20201030 Windows: use binmode on STDERR/STDOUT to prevent line ending
           translation.
* 20201031 Windows: Fix setting options via environment variable  (this fix
           now means that -S is not supported on Windows)
* 20201031 Windows: Explicitly revoke support for providing header names
           embedded in environment variable names (%SWAKS_OPT_header_From%
           will result in an error)
* 20201031 Allow a value of '<>' in an environment variable to mean empty
           string.  Usable everywhere, but needed on Windows since it doesn't
           support set-but-empty environment variables.
* 20201101 Windows: Allow LOGNAME environment variable to override default
           email sender
* 20201101 Change the generation of help text from a manual perldoc call to
           Pod::Usage
* 20201106 Testing: Extensive changes to allow test tools to run on Windows
           cmd.exe
* 20201107 Add ..TOKEN.. form of tokens to more-easily allow tokens to be
           specified from Windows command line.
* 20201116 Previously changed %DATE% to %MESSAGE_ID% in some tests to protect
           against accidental expansion on Windows.  Change back to ..DATE..
           now that it's an option.
* 20201126 Add tools for visualizing errors caused by line ending issues
* 20201126 Rework interactive tests and enable on Windows
* 20201126 Turn releases back on and add release for v20201024.0 (#13)
* 20201126 --output-file (and -stdout and -stderr) now truncate the output
           file if it already existed.
* 20201126 --port and --local-port should override a port set in --server or
           --local-interface.
* 20201222 Add transaction-level tests
* 20201223 Add missing option hints from --proxy-version documentation
* 20201223 Remove documentation note about which Authen::NTLM version to use,
           there's only one on CPAN anymore.
* 20201223 When an unsupported XCLIENT attribute is requested, print the
           missing attribute in the error.
* 20201223 Don't print "Exiting" in the XCLIENT attempted but failed message.
           It is always unnecessary (Auth doesn't print it) and is wrong
           when --xclient-optional is in use.
* 20201223 Fix several --drop-after-send bugs and inconsistencies.
* 20201224 Fix --quit-after/--drop-after/--drop-after-send with XCLIENT
           stop-point when XCLIENT is not negotiated.
* 20201224 Add new stop-point XCLIENT-HELO to address lack of specificity
           when mixing XCLIENT and HELO stop-point.
* 20201224 In --dump output, print a raw proxy string in base64 if version 2
* 20201224 --proxy argument can now (optionally) be provided with the protocol
           prefix.
* 20201224 Allow --proxy to read string from a file, and allow it to be
           base64 encoded.
* 20201224 Change error message when missing --proxy* option to list every
           missing option instead of just the first missing.
* 20201224 --no-hints should also include --no-info-hints, analogous to
           --hide-informational/--hide-all which already worked this way.
* 20201225 Add autocat feature to test tools
* 20201225 Add exit code checking to regression tests
* 20210116 Fix dead link to openssl cipher list format documentation in
           --tls-cipher doc.
* 20210116 Update copyright year to 2021
* 20210116 Add TLS informational line showing whether client certificate was
           by the server and whether swaks sent it.
* 20210116 Use named constant in TLS code instead of magic number
* 20210117 Add TLS informational line showing the verification status of
           the server certificate.
* 20210117 Change --tls-verify to require both cert and hostname verification.
           Add --tls-verify-ca (previous behavior of --tls-verify) and
           --tls-verify-host to just require verification of the certificate
           against the local CA or the target hostname or IP, respectively.
* 20210117 Add --tls-verify-target to set a host to be used for verification,
           overriding the internal connection information.
* 20210123 Clarify --auth-optional-strict documentation
* 20210123 Adding --cc and --bcc options.
* 20210123 Add PROXY as a valid --quit-after, --drop-after, and
           --drop-after-send argument.
* 20211208 Fix deprecated use of --body in doc (#39)
* 20220505 Update copyright year to 2022
* 20220505 Fix double-entry for Linux on installations.html #46
* 20220505 Fix broken POD formatting for the default --data argument #47
* 20231103 Update copyright year to 2023
* 20231103 Fix unreliable results certificate/hostname verification
* 20231104 Switch to using IO::Socket::IP by default.  Fall back to
           IO::Socket::INET/INET6; deprecate the use of these libraries #43
* 20231105 Remove deprecated ability to infer filename vs. inline data for
           --data, --body, --attach, and --attach-body options
* 20231105 Fix bug which caused the "I really mean a filename" @sigil to
           be included in the attachment filename in the body of the email.
           It is now stripped from the filename before use (#55)
* 20231110 implement --tls-chain (based on initial implementation by
           Wolfgang Karall-Ahlborn in #60)
* 20231126 In prep for handling peer chains properly, change the printing
           of local cert labeling from local/chain to local[i], but
           only if there is more than one local cert.
* 20231130 Flesh out printing of certs to include notBefore/After,
           commonName (not as part of the DN), and subjectAltName
* 20231130 Show debug for entire peer cert chain, not just for the end
           cert (#73)
* 20231202 Add --tls-get-peer-chain option.  Analogous to --tls-get-peer-cert
           but shows every cert sent by the peer, not just the first one (#72)
* 20231203 Reorganize TLS internals to make support easier
* 20231203 Switch method for getting peer certs, making debug way more
           reliable
* 20231203 Clean up the output when we print local and peer cert info, make it
           more compact and uniform
* 20240102 Update copyright year to 2024
* 20240102 Print all available certificate information even if TLS was not
           successfully negotiated
* 20240102 Rework tls verification. On cert verification failure, print
           openssl's error message.  Get rid of verify callback.
* 20240102 Clarify --tls-verify-ca docs, this verifies both signing and
           date expiration (notBefore/notAfter)
* 20240103 Previously-deprecated option -g removed entirely
* 20240103 Improve test suite experience on FreeBSD
> 20240103 released 20240103.0
            </code></pre>
        </div>
    </div>


    <div class="row">
        <div class="two columns">
            <b>20201014.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20201014.0.tar.gz">distribution</a>,
                <a href="./files/swaks-20201014.0/swaks">script only</a>,
                <a href="./files/swaks-20201014.0/doc/ref.txt">version docs</a> (<a href="https://github.com/jetmore/swaks/blob/v20201014.0/doc/base.pod">rendered</a>),
                <a href="https://www.jetmore.org/john/blog/2020/10/swaks-release-20201014-0-available/">announcement</a>
            </p>
            <p>
                <b>Change Summary:</b>
            </p>
            <pre><code>
New Features:
  * None
Notable Changes:
  * None
Notable Bugs Fixed:
  * Last release introduced a bug where Date: headers were localized, which
    is against RFC.  Further, that localization then broke character rendering
    in some locales.  A new fix for the original issue (#17) was put in place,
    which no longer localizes the Date: header and fixes the newly introduced
    rendering issue (#25)
  * Last release introduced a bug which prevented --protect-prompt from
    working.  This is now fixed (#26)
            </code></pre>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20201011 Fix test scripts to munge copyright date
* 20201012 Fix date string - should always be in English, not localized
           (introduced in 434f494abcc3558c73efc0e57a4338adeb402253)
* 20201013 But actually fix the date string this time. Also fix #25
* 20201014 Fix --protect-prompt. Not implemented correctly after option
           processing overhaul in a46b929 #26
* 20201014 Add newline echo after entering password with --protect-prompt
> 20201014 released 20201014.0
            </code></pre>
        </div>
    </div>


    <div class="row">
        <div class="two columns">
            <b>20201010.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20201010.0.tar.gz">distribution</a>,
                <a href="./files/swaks-20201010.0/swaks">script only</a>,
                <a href="./files/swaks-20201010.0/doc/ref.txt">version docs</a> (<a href="https://github.com/jetmore/swaks/blob/v20201010.0/doc/base.pod">rendered</a>),
                <a href="https://www.jetmore.org/john/blog/2020/10/swaks-release-20201010-0-available/">announcement</a>
            </p>
            <p>
                <b>Change Summary:</b>
            </p>
            <pre><code>
New Features:
  * Added .netrc support
  * Added --tls-sni option
  * Swaks is now available on CPAN as App::swaks
  * Swaks will now print errors if deprecated functionality is used
Notable Changes:
  * Automatic file detection is deprecated.  Previously, if an argument to
    --data, --body, --attach-body, and --attach resolved to an openable file,
    the contents of that file would be used as the actual argument.  Now the
    proper way to do this is to place '@' in front of the argument to
    state explicitly that the argument contents are in a file.
  * If any of the --xclient-* family of options (--xclient-name,
    --xclient-addr, etc) is provided more than once, only the last option
    provided will be used. See --xclient option if you need to simulate
    the previous behavior
  * -g option is now deprecated
  * Time::Local is no longer used and POSIX is now listed as a required
    module
Notable Bugs Fixed:
  * Fix for subtle issue related to environment variable options.  Affected
    error handling for options which required args.
  * Fix issue preventing XCLIENT and STARTTLS from working together
    properly (#21)
  * Fix issue which could cause generated date header to oscillate on
    the day of DST transition (#17, deb bug 955798)
            </code></pre>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20191008 Added skeleton of installation doc (missingcharacter)
* 20191009 Added .netrc support (fany)
* 20191012 Added --tls-sni option (lightsey)
* 20191012 Fix bug which treated `SWAKS_OPT_foo=` as `--foo ''` instead of
           `--foo`.  This affected error handling for options which
           required args.
* 20191019 Reimplementation of option processing fixed lots of missed edge
           cases, especially options which were documented to require an
           argument but didn't error if none was provided (--add-header,
           --header, --attach, --attach-type, --attach-body, possibly others)
* 20191021 The --xclient-name, -addr, etc options can now only be provided
           once per call. Multiple uses of one of these options will result
           in only the last one being used.  Previously these options could
           be set multiple times to send the same xclient attribute multiple
           times.  If that's needed, see the --xclient option.
* 20191022 The "no-" option negating now works much better (previously was
           hit-or-miss on special option families like --attach*, --xclient*,
           and --header*).
* 20191023 Ensure "main" choice defined first in each option's list of
           aliases because the first item is used for errors.
* 20191026 Deprecate -g option.  Change current implementation so it is
           rewritten to `--data -` before option processing. Any occurrence
           of `--data` will cause -g to be discarded. "no-" prefix will not
           negate.
* 20191026 Update --data, --body, --attach-body, and --attach so that a
           leading '@' forces the argument to be a filename. Deprecate
           previous "if it happens to be an openable file" behavior
* 20191027 Do service name resolution on --local-port if non-numeric
* 20191028 Fix bug setting filename in attachments when '@' prefix used
* 20191028 Add deprecation warning system.  Warnings in code and a table
           of deprecated functionality and removal dates in documentation.
* 20191111 Switch Getopt to process an arbitrary list instead of manipulating
           ARGV for each call.
* 20191111 Check to see if there are any entries left in the option list
           after each stage of option processing and error if so.
* 20191111 Rework the command line reconstruction so that options that
           don't take an argument do not include one in the reconstruction.
* 20191111 Unexpected argument format for --timeout now errors and exits
           instead of silently using default timeout.
* 20191111 Document that --timeout argument can use 'h' as a modifier.
* 20191111 Docs: Reorganize Terms and Conventions, add Option/Arg/[]/<>.
* 20191112 Docs: Be consistent in how <> and [] are used in argument docs.
* 20191113 Docs: Define option types and assign to every option.
* 20191113 Docs: General typo and consistency fixes.
* 20191122 Docs: Establish project standards for POD formatting and
           make first pass at applying.
* 20191220 Docs: Fix incorrect example used in --attach-body definition.
* 20191221 Docs: merge OpossumPetya's web page changes, use better web
           practices and make mobile friendly
* 20200118 Docs: misc web page changes. tweak pre font size to prevent
           scrolling, remove stray quotes, remove the news section from index
           add announcement to versions, link to both plaintext and
           rendered docs, etc
* 20200425 Fix strange issue where GMT offset could oscillate on days of DST
           transition.  Time::Local is no longer used and POSIX is now
           required (#17, deb bug 955798)
* 20200801 Update copyright year to 2020
* 20200801 Fix bug which broke XCLIENT and STARTTLS used together (#21)
* 20201004 Update README.md to reflect changes from
           234db829dc6e6d45fe4da5f2bef075828ebc0ac7 (#22). Move to reference-
           style markdown links to make it easier to identify links that
           need to be changed for a release.
* 20201004 Fix wording and example around html body on faq.html (#14)
* 20201004 Add '@' in attach examples to prevent deprecation warnings
* 20201004 Convert installation.pod to installation.html, link to it from
           index.html and README.md, and flesh it out. (#7)
* 20201010 Package App::swaks-20190914.0 for CPAN distribution (#23)
> 20201010 released 20201010.0
            </code></pre>
        </div>
    </div>


    <div class="row">
        <div class="two columns">
            <b>20190914.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20190914.0.tar.gz">distribution</a>,
                <a href="./files/swaks-20190914.0/swaks">script only</a>,
                <a href="./files/swaks-20190914.0/doc/ref.txt">version docs</a> (<a href="https://github.com/jetmore/swaks/blob/v20190914.0/doc/base.pod">rendered</a>),
                <a href="https://www.jetmore.org/john/blog/2019/09/swaks-release-20190914-0-available/">announcement</a>
            </p>
            <p>
                <b>Change Summary:</b>
            </p>
            <pre><code>
New Features:
  * Source is now available on github.com/jetmore/swaks
  * Added --body-attach option to allow more granularity in setting body
    information
  * Added 'data' and 'dot' as valid --drop-after-send and
    --drop-after arguments
  * Added %NEWLINE% as a new --data token
Notable Changes:
  * Options provided via environment variable are now sorted before
    processing to provide a deterministic processing order
  * Option bundling is no longer enabled.  This fixes several option
    processing oddities, like "-foobar" being interpreted as
    "-f oobar"
  * If the arg to --data looks like a file but is not openable, error
    and exit instead of using it the file name as the raw data value
  * Remove interactive prompts for --helo and --from when hostname cannot
    be determined internally, just error  and exit instead. If the user
    was not expecting an interactive experience, don't start one
  * Remove re-prompting for port when an invalid service name was supplied,
    just error and exit instead.  If the user was not expecting an
    interactive experience, don't start one
Notable Bugs Fixed:
  * Handle malformed headers more gracefully in header replacement
  * Fix bug causing the processing of options  prefixed with the negating
    "no-" to work unreliably
  * --version and --help should work even if they aren't the very
    first option
  * -S is now a distinct option from -s, as documented
  * Fix bug preventing the --option=arg option format from being
    unusable with --header and --attach* options
            </code></pre>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20181110 --add-header documentation was still referencing a single-char,
           no longer valid, replacement token. Replace with the correct token.
* 20181110 Doc fix for default body - %SWAKS_VERSION% missing trailing char.
* 20181110 Fix issue with malformed headers.  Don't fall over if header
           doesn't contain a colon or looks like an illegal continuation.
* 20181110 Fix --attach* option processing to remove possibly ambiguity
* 20181110 Implement --body-attach option to allow more granularity in
           setting body information (different mime types, alternatives, etc).
* 20181201 Config file fixes around searching default $SWAKS_HOME, $HOME,
           and $LOGDIR locations:
             - Searching default locations for the first existing
               PATH/.swaksrc did not actually work as documented.
             - If none of the default search environment variables was set,
               Swaks would not process the "portable" defaults optionally
               stored in the actual swaks script.
* 20181202 Since there is no inherent order to options provided in environment
           variables, sort them before processing to define an order.
* 20181202 Document the general rule that when processing duplicate options,
           the last option specified wins, both inter- and intra-method.
* 20181202 Document the unreliability of using environment variables to unset
           other environment variable options with the "no-" prefix.
* 20181203 Fix bug causing in "no-" option processing to work unreliably.
* 20181204 Tidying and clarifying the OPTION PROCESSING section of the docs.
* 20181204 Adding an ENVIRONMENT VARIABLES section to the doc.
* 20181208 When processing config file options with no leading '-' and any
           environment variable config, prefix the option with '--' for
           processing, not '-'. Bandaid for very minor difference between
           '-' and '--' option processing which I hope to fix soon.
* ******** --version and --help should work even if they aren't the very
           first option.
* ******** Add a flag for --dump-mail in the OUTPUT section of --dump
* ******** Turn on case-sensitivity for configuration options.  Needed to
           make -S distinct from -s, as documented.
* ******** Turn off option bundling. No practical use and it could cause
           real confusion (with bundling turned on, -foobar was "-f oobar"
           instead of an unknown option.
* ******** Add validation to --proxy-family (when proxy-version=1) and
           --proxy-version options.
* ******** --copy-routing should error when no argument given.
* ******** Update copyright year to 2019
* ******** Add documentation for missing --quit-after synonym STARTTLS
* ******** Adding data and dot as valid --drop-after-send and --drop-after
           arguments
* ******** Typo in documentation for --ehlo, reported by Konstantin Stephan
* ******** Clarify how XCLIENT arguments are grouped in --xclient doc
* ******** Enforce key=value format for arguments to --auth-extra and
           --auth-map
* ******** Small code tidy around %DATE% token replacement
* ******** Add %NEWLINE% as a new --data token
* ******** If the arg to --data looks like a file but is not openable,
           error and exit instead of using it the file name as the raw
           data value
* ******** --attach option processing was calling die() instead of
           ptrans/exit on error
* ******** Fix handling of --option=arg option format which prevented it
           from being used with --header and --attach* options
* 20190814 --tls-optional-strict was incorrectly marked internally as
           optionally accepting an argument
* 20190814 --use-old-data-tokens was not completely removed, clean up
* 20190815 Updating copyright year to 2019
* 20190815 --protocol's argument was incorrectly marked as optional
* 20180816 Rework how the --show-time-lapse option is tracked internally
           and displayed in --dump output
* 20190817 Rearrange internal option definition structure in preparation
           for major rework
* 20190817 Remove interactive prompts for helo and from when hostname
           cannot be determined internally.  Just error instead.
* 20190817 Cleaning up error messages that contained extra newlines
* 20190817 Remove re-prompting for port when an invalid service name was
           supplied. Just error and exit instead
* 20191005 Fixed typos in base.pod and recipes.pod
> 20191006 released 20190914.0
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>v********.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-********.0.tar.gz">distribution</a>,
                <a href="./files/swaks-********.0/swaks">script only</a>,
                <a href="./files/swaks-********.0/doc/ref.txt">version docs</a>,
                <a href="https://www.jetmore.org/john/blog/2018/11/swaks-release-********-0-available/">announcement</a>
            </p>
            <p>
                <b>Change Summary:</b>
            </p>
            <pre><code>
New Features:
  * Added --dump-mail option.
  * Added --xclient-delim, --xclient-destaddr, --xclient-destport,
    --xclient-no-verify, and --xclient-before-starttls options.
Notable Changes:
  * XCLIENT can now send multiple XCLIENT requests.  Because of this,
    --xclient and --xclient-ATTR values are no longer merged into one
    string.  This breaks previously documented behavior.
  * Numerous improvements to the output of --dump and --dump-as-body,
    including the ability to limit output by section, layout improvements,
    adding missing options to output, and fixing bugs.
Notable Bugs Fixed:
  * Fixed bug preventing Proxy from working with --tls-on-connect.
  * XCLIENT is now sent after STARTTLS to match with Postfix's expectations.
  * Fixed bug which could allow mail sending to proceed without a valid
    recipient.
  * Replacing a multi-line header via --header or --h-HEADER now replaces
    the entire header, not just the first line.
  * The option for specifying the local port was documented as --local-port
    but implemented as --lport.  Both are now documented and implemented.
  * Fixed two bugs which prevented interactions between --dump,
    --auth-hide-password, --dump-as-body, and --dump-as-body-shows-password
    from producing consistent output.
            </code></pre>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20170106 Fix typos in base.pod (reported by Andreas Metzler/Debian)
* 20170307 Fix spelling, grammar, and phrasing issues in base.pod, faq.html,
           and index.html
* 20170307 Remove deprecated single-character data tokens and
           --use-old-data-tokens option
* 20170308 Implement --dump-mail option
* 20170308 Add BCC section to faq.html
* 20170819 Enable https access for website
* 20180217 Updating copyright date to 2018
* 20180217 Fix bug preventing Proxy from working with --tls-on-connect
* 20180217 Remove note in documentation that Proxy v2 support is theoretical.
           Exim fixed v2 bug in 4.89 and swaks has now been tested with it
* 20180225 Change Swaks to send XCLIENT after STARTTLS (per user bug
           report and Postfix mailing list confirmation).
* 20180225 Add --xclient-before-starttls option to allow Swaks to attempt
           XCLIENT first and then STARTTLS.
* 20180225 Added license and authorship info to README, base.pod, index.html
* 20180609 Moved xtext encoding out of do_smtp_xclient and into to_xtext
           in preparation for implementing xforward, which will also need it
* 20180609 Add --xclient-delim option to allow --xclient-ATTR options to be
           grouped into individual XCLIENT calls
* 20180609 Now that we support multiple XCLIENT calls, --xclient-ATTR options
           are no longer merged with --xclient into a single XCLIENT call.
           This breaks previously documented behavior.
* 20180609 Add --xclient-no-verify added to turn off requirement that
           XCLIENT attributes must have been advertised by server for Swaks
           to send them
* 20180609 If an option-processing callback fails to load, print error and
           exit (mostly helpful for development)
* 20180609 Add --xclient-destaddr and --xclient-destport options
* 20181030 Support TLSv1.3 and DTLSv1.2 in debug strings
* 20181101 Don't allow empty recipient if we're prompting for one
* 20181101 --dump and --dump-as-body now take optional arguments which can
           limit what is printed, making targeted debugging easier.
* 20181101 --dump now prints all output to the default output filehandle
           instead of STDOUT.
* 20181101 Using --header now handles replacing a multi-line header properly
* 20181103 Prevent --dump from always listing XCLIENT as active
* 20181103 -lp/--local-port was documented but -lp/-lport was implemented.
           Now all three are documented and implemented.
* 20181103 Properly document that --drop-after-send takes an argument.
* 20181103 Rework --dump to produce more consistent and thorough output
             - ensure no leading/trailing space when only dumping sections
             - alphabetize lists for reproducible output
* ******** Fix bugs causing the interactions between --dump,
           --auth-hide-password, --dump-as-body, and
           --dump-as-body-shows-password to not always work as documented
* ******** More --dump improvements and bug fixes
             - General layout improvements
             - Auth: add output for --auth-hide-password
             - Transport: only show the output options relevant to the type
             - TLS: Fix bug preventing display of TLS info when
               --tls-on-connect used
             - Proxy: Add output for --proxy-command and --proxy-protocol
             - Transport: add output for --copy-routing
             - Transport: add output for -4 and -6
             - Output: add output for --protect-prompt and --show-raw-text
             - Protocol: add output for --force-getpwuid
             - XCLIENT: add output for --xclient-before-starttls and
               --xclient-no-verify
* ******** --dump-mail should write to the default output filehandle
* ******** Add --output as a synonym for --output-file
* ******** Clean up contact/notifications - remove Google+ everywhere, add
           RSS and Twitter to base.pod
* ******** Spelling fixes
* ******** Rename LICENSE and README to have .txt extension
* ******** Update release tooling after development environment changes
> ******** released ********.0
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>v20170101.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20170101.0.tar.gz">distribution</a>,
                <a href="./files/swaks-20170101.0/swaks">script only</a>,
                <a href="./files/swaks-20170101.0/doc/ref.txt">version docs</a>,
                <a href="https://www.jetmore.org/john/blog/2017/01/swaks-release-20170101-0-available/">announcement</a>
            </p>
            <p>
                <b>Change Summary:</b>
            </p>
            <pre><code>
New Features:
  * Added Per-Recipient Data Response (PRDR) support
    (see https://tools.ietf.org/html/draft-hall-prdr-00).
  * Added PROXY protocol support
    (see http://www.haproxy.org/download/1.5/doc/proxy-protocol.txt).
  * Added --drop-after and --drop-after-send options.
  * Added %MESSAGEID% expansion token.
  * Added %SWAKS_VERSION% expansion token.
Notable Changes:
  * Default DATA template now includes a Message-Id: header.
Notable Bugs Fixed:
  * Fixed incorrect range on XCLIENT rfc1891/xtext encoding.
            </code></pre>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20130422 Fixed typo in man page ("test" -> "text"), reported by
           Graeme Hewson.
* 20130906 Fixed some POD formatting errors. Exit code formatting reported by
           both Debian and redhat maintainers, caused by Pod::Simple change
           in perl-5.18.1.  Others found playing with podchecker.
* 20140207 Updating copyright to 2014
* 20140207 Updated faq.html with typo and phrasing fixes.  Added note that
           Homebrew and MacPorts both package swaks (h/t Andrew Langhorn)
* 20141128 Fixed local_cert_subject logging bug, reported by Roman Rybalko
* 20141128 Fix incorrect range on XCLIENT rfc1891/xtext encoding caused by
           mixing hex and decimal. Reported by Thomas Szukala
* 20141202 Fixing a couple of "an [CONSONANT]" doc typos
* 20150128 Handle the case where a server advertises XCLIENT but does not
           advertise any supported attributes.
* 20150212 Add %MESSAGEID% token and add Message-Id as a part of the default
           DATA.
* 20150212 Add %SWAKS_VERSION% token and use it by default in the X-Mailer
           header.  Suggested by Jan-Pieter Cornet.
* 20151109 Fixed grammar issue in "About the Name" section
* 20161230 Updating copyright to 2016
* 20161230 Added tester to check for option and okey collisions in
           @G::raw_option_data
* 20161230 Added --drop-after and --drop-after-send options
* 20161231 Remove caveat from --pipeline documentation
* 20161231 Add --prdr support
* 20170101 Updating copyright date to 2017
* 20170101 Add PROXY protocol support
* 20170101 Removed some cruft from do_smtp_gen (noticed by accident)
* 20170101 Removed unneeded extra functionality added to transact() for
           PRDR implementation (functionality already existed)
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>v20130209.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20130209.0.tar.gz">distribution</a>,
                <a href="./files/swaks-20130209.0/swaks">script only</a>,
                <a href="./files/swaks-20130209.0/doc/ref.txt">version docs</a>,
                <a href="https://www.jetmore.org/john/blog/2013/02/swaks-release-20130209-0-available/">announcement</a>
            </p>
            <p>
                <b>Change Summary:</b>
            </p>
            <pre><code>
New Features:
  * Support for the XCLIENT SMTP extension
    (see http://www.postfix.org/XCLIENT_README.html)
  * Added --no-send-hints, --no-receive-hints, and --no-info-hints
    output control options
  * The TLS subsystem got a major face lift, including
    * Added --tls-cert and --tls-key options to specify the certificate
      Swaks will use when negotiating TLS (Debian bug 497654)
    * Added more error checking and logging around protocol negotiation
    * Added tlsv1_1 and tlsv1_2 as "known" protocol versions
    * Added --tls-protocol option
    * Added --tls-cipher option
    * Added --tls-verify option
    * Added --tls-ca-path option
Notable Changes
  * The TLS information lines have changed some:
    * Changed "w/" to "with" and "peer subject DN" to "peer DN"
    * Changed the TLS cipher line from just NAME to VERSION:NAME:BITS
    * Added new line stating the DN of local cert or that none is being used
    * If the negotiated protocol version is unknown, print raw version number
  * Changes to --dump output
    * Added --tls-get-peer-cert setting
    * Added locally-available TLS/SSL protocol versions
  * Swaks no longer attempts to send QUIT down a connection when TLS
    negotiation fails.  This may cause issues with the use of --tls-optional
Notable Bugs Fixed
  * Previous release did not properly "cancel" SASL session when server
    did not behave properly (reported by Erwan Legrand)
  * Swaks would send QUIT twice in specific cases involving mail rejections,
    the --quit-after option, and --pipeline
  * Swaks would die silently during errors in the SSL protocol negotiation
    (-tlsc -p 25 would cause it)
  * Swaks could fail to handle the end of a TLS session over a pipe transport
    when the server has closed the connection but Swaks is expecting to read
    more data
            </code></pre>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20120330 add --tls-get-peer-cert to --dump output
* 20120330 add --tls-cert and --tls-key options to allow swaks to use
           certs for its half of the session.  Debian bug 497654,
           Luis E. Muñoz provided a patch that was the basis for the
           eventual implementation.
* 20120330 cleaned up TLS info lines.  Changed "w/" to "with", changed
           "peer subject DN" to "peer DN".
* 20120330 Add a new TLS info line, either logging the DN of the local
           cert or print that no local cert is being used
* 20120330 A bit more TLS error checking.  Some added on general principle.
           Specifically fixed error where swaks would die silently during
           SSL protocol negotiation (could replicate by doing -tlsc
           against port 25)
* 20120330 Change the TLS cipher info line from just the NAME to
           VERSION:NAME:BITS
* 20120402 xclient first pass.  Added -xclient* options to data struct
           made first pass at documenting feature and options
* 20120402 xclient second pass.  option processing and --dump
* 20120404 implemented the SMTP portion of XCLIENT, documented the
           --xclient-optional(-strict)? behavior.  Added and documented
           33,34 exit codes
* 20120404 add and document new --quit-after option 'xclient'
* 20120404 added xtext encoding for the xclient options
> 20120405 released 20120405.0-dev to Kevin San Diego and Kurt Anderson
           (submitters of independent XCLIENT patches) for comment
* 20120407 fix bug w/ xclient impl, using just --xclient didn't cause
           XCLIENT to be sent
* 20120410 typo fixes and clarifications for XCLIENT docs
* 20120529 Added some more error checking in TLS code, breadcrumbs to the
           existing errors (spurred by Debian bug 675009, though the
           specific issue in that bug was fixed by commit 311).
* 20120530 Fix the botched TLS checks in the last commit
* 20120530 In a secure session, if the protocol version is unknown, print
           the raw version number in the post-tls-negotiation info lines
* 20120530 Add tlsv1_1 and tlsv1_2 as "known" protocols in the
           post-tls-negotiation info lines
* 20120531 Change around tls_get_peer_cert processing so that it's not
           touched if TLS isn't going to be attempted
* 20120531 Add "available protocols" to the tls section of --dump
* 20120531 fixed wrong exit code for improper use of --tls-key/--tls-cert
* 20120531 Added in --tls-protocol option to specify or exclude specific tls
           protocols.
* 20120531 Added --tls-cipher option to set preferred ciphers list
* 20120601 Added --tls-verify option
* 20120624 If TLS is attempted but failed, just exit, don't attempt to
           send quit because we don't know what state the cxn is in.  This
           will likely cause problems with tls-optional connections,
           documenting the concern there.
* 20120624 Added --tls-ca-path option to specify alternate certs for use in
           verification
* 20120710 Typo/phrasing review of RELEASE/README.  Adding doc for release
           process (failed to update README for 20120320.0)
* 20120728 Reworked website, placing all files in swaks/files and redirecting
           where necessary; simplified index.html to cut down on casual
           consumer confusion; split version/change information into
           version.html; applied consistent style to all pages; new style
           guideline, changing "swaks" to "Swaks" when used as proper noun
* 20120803 Fixed probably-never-traversed bug in last-resort transaction hint
* 20120803 Implemented new --no-send-hints, --no-receive-hints, and
           --no-info-hints options (mostly so I could can copy certs from
           the output of --tls-get-peer-cert).
* 20120805 Fixed bug in TLS and pipe recv code when server has closed
           connection but swaks is expecting data to be present
* 20120805 Fixed bug causing swaks to "double-quit" when a rejected MAIL of
           RCPT was sent _AND_ a --quit MAIL or --quit RCPT was used _AND_
           --pipeline as used.
* 20121014 Fixed to send proper "cancel" of SASL transaction (bare '*') when
           the server returns an improper rspauth.  swaks would understand
           that auth failed, but later than it should have (reported by
           Erwan Legrand)
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>v20120320.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20120320.0.tar.gz">distribution</a>,
                <a href="./files/swaks-20120320.0/swaks">script only</a>,
                <a href="./files/swaks-20120320.0/doc/ref.txt">version docs</a>,
                <a href="https://www.jetmore.org/john/blog/2012/03/swaks-release-20120320-0-available/">announcement</a>
            </p>
            <p>
                <b>Change Summary:</b>
            </p>
            <pre><code>
New Features:
  * IPv6 support
  * -4 and -6 options to force IPv4 or IPv6.
  * Added --local-port option
  * Added --dump-as-body and --dump-as-body-shows-password options, allowing
    session configuration information to be sent in the test email itself.
    (Suggested by Chris Pimlott)
  * --dump (and therefore --dump-as-body) includes a new line for a
    "reconstructed command line".  This is a sort of a synthetic command
    line including the real command line, any environment variable configs,
    and any config files.
Notable Changes
  * The DIGEST-SHA1 authentication type now requires the Digest::SHA
    perl module instead of Digest::SHA1.  Digest::SHA1 has always been
    an extra install step, while Digest::SHA has been in the core perl
    distribution for many years. (Suggested by Andreas Metzler)
  * The -m option, marked deprecated since 2007, has been removed.
Notable Bugs Fixed
  * In some very rare cases, a server response can be a base64-encoded
    success response, or a plaintext failure.  Previously in that
    situation swaks did not handle the case of the plaintext error and
    the --auth-plaintext option well, resulting in confusing output.
            </code></pre>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20120101 typo fix in docs (rep. Andreas Metzler)
* 20120101 changed the indent formatting so all the examples are at a
           consistent indent.
* 20120101 Finally documenting the --dump option
* ******** Removed the code for the -m option which had been deprecated
           since ********.
* ******** Updating copyright note through 2012
* ******** Changed swaks to build version string dynamically for dev builds;
           updated gen-release.pl's method for setting release versions
* ******** Added "ABOUT THE NAME" section to base.pod
* ******** Removed final breadcrumbs of removed --input-file and additive
           --silent from docs
* ******** First pass at IPv6 support.  Static routing only, implemented -4 and
           -6, no MX logic yet.
* ******** updated --server parsing to allow alternate syntaxes for specifying
           port (SERVER, [SERVER], [SERVER]:PORT, IPV4_OR_HOST:PORT,
           SERVER/PORT, [SERVER]/PORT)
* ******** Added --local-port option, updated --local-interface to support
           specifying a local port using same logic at --server
* ******** Slight change in connection failure error - local interface only
           shown if one was set in the first place
* ******** changed several instances of "destination" to "target" in base.pod
           to stay consistent with "terms and conventions" section.
* ******** MX tweaks to try to find exchangers with the correct IP type when
           -4 or -6 are used
* ******** Rearrange --dump output, group settings closer to how options
           are organized in the docs
* ******** still more removal of code supporting the -l option (r253 removed
           the function parsing the file, but we still checked the now-
           never-populated $fconf in lots of option processing)
* ******** change all references for Digest::SHA1 to Digest::SHA, included
           in perl core since at least 5.9. Suggested by Andreas Metzler
* ******** fixed a couple of exit codes to be more specific
* ******** There are some interactions (DIGEST-MD5 specifically) which can
           result in b64-encoded response if successful, plaintext response
           if an error.  --auth-plaintext didn't play well with the
           plaintext error.
* 20120320 added reconstruct_options() to generate an effective command line.
           Added to --dump output
* 20120320 add --dump-as-body and --dump-as-body-shows-password.
* 20120320 twiddled the --dump code to allow for reuse with --dump-as-body.
           noted that plaintext passwords can be shown i --dump output.
* 20120320 Small patch to handle (-6 not set, --server IPv6, ipv6 not
           available) gracefully. Report from and patch by Ulrich Zehl.
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>v20111230.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20111230.0.tar.gz">distribution</a>,
                <a href="./files/swaks-20111230.0/swaks">script only</a>,
                <a href="./files/swaks-20111230.0/doc/ref.txt">version docs</a>,
                <a href="https://www.jetmore.org/john/blog/2011/12/swaks-release-20111230-0-available/">announcement</a>
            </p>
            <p>
                <b>Change Summary:</b>
            </p>
            <pre><code>
New Features:
  * Added --output-file family of options to capture some or all output
    without requiring shell redirection
  * Added --protect-prompt option to protect "sensitive" user input
    (currently only auth passwords)
  * Added --auth-hide-password to replace recoverable passwords in SMTP
    session output with a dummy string
  * Added --auth-extra to have a single interface to pass non-standard
    authentication options into swaks.  Currently used by NTLM and
    DIGEST-MD5.
  * Added --show-raw-text option to provide more details of exact data
    being sent on the wire
Notable Changes
  * SMTP Data token parsing has been changed from single-character (%F) to
    multi-character (%FROM_ADDRESS%) to reduce chance of collisions with
    message text, especially non-ascii language encoding.  See
    --use-old-data-tokens to recover old tokens while transitioning.
    (issue reported by Peter Baranyi)
  * Specifying the NTLM/MSN/SPA DOMAIN by appending "%DOMAIN" to the
    username is no longer supported.  See the new --auth-extra command.
  * The DIGEST-MD5 authentication no longer uses the (buggy)
    Authen::DigestMD5 module.  The Authen::SASL module is now used.
Notable Bugs Fixed
  * Previous release broke ability to use --header to set custom headers
    (first reported by David Favor)
  * Previous release would not allow a file to be used to provide the
    message data if the file name contained the character "n".
    (first reported by dietrich.rebmann@******)
  * swaks has never properly handled creating date strings for timezones
    that are not on an even hour boundary.
    (report from, and patch provided by, Peter Samuelson)
  * The documented behavior of the "no-" prefix on swaks' options did not
    work when --OPTION and --no-OPTION were given in the same context.
  * The DIGEST-MD5 auth type was not completely implemented.  Worked
    extensively with Erwan Legrand to get as complete and useful an
    implementation as possible.
  * Previous release contained a regression in which TLS responses sent in
    multiple packets were not processed correctly.
    (reported by Peter J. Holzer)
            </code></pre>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20100212 fixed doc typo
* 20100215 added recipes.pod to repo and added vrfy and fswaks recipes
* 20101217 added and documented the --output-file* options
* 20101217 fixed bug where --header no longer worked for setting custom headers
           (reported 20101015 by David Favor)
* 20110309 updated copyright date to 2011
* 20110309 misc typos/style
* 20110309 added --protect-password option to make best effort not to
           echo user input on sensitive fields (*nix and Win32)
* 20110321 fixed bug which prevented message data to be provided in a file
           with a "n" or "\" in the filename.  Reported and fix suggested
           by dietrich.rebmann#freenet.de
* 20110321 added --auth-hide-password option to replace recoverable passwords
           in SMTP transactions with a dummy string
* 20111021 Fixed the date string TZ offset to work correctly for TZs that
           are not fixed on an hour boundary.  Report and patch by Peter
           Samuelson in Debian bug 646084.
* 20111128 Changed default token parsing from the old single-char version
           (%F) to the newer, safer version (%FROM_ADDRESS%).  Added and
           and documented the --use-old-data-tokens option to get old
           behavior back.  Marked option and older tokens as deprecated
           to hopefully get people to transition to new tokens. (The issue
           with the overly simplistic tokens and non-ascii language encoding
           first reported by Peter Baranyi)
* 20111128 Adding missing --no-data-fixup option back into the docs
* 20111128 removed the --input-file/-l option from the code.  Left one final
           mention on if in the docs as a breadcrumb to anyone who still needs
           to move to --config.  Will remove breadcrumb after next release
* 20111128 Removed the additive nature of the --silent/-S option.  Left
           a mention of it in doc as breadcrumb.  Remove all mention in next
           release
* 20111128 Fix option processing so that "no-" prefixed options wipe out
           the same option when presented in the same context.  Previously
           --no-timeout on command line would erase a timeout set by a
           config option or env variable, but would not erase --timeout
           given earlier on the same command line.
* 20111130 Changed the DIGEST-MD5 backend from Authen::DigestMD5 to
           Authen::SASL.  Authen::DigestMD5 had some blatant protocol
           bugs and swaks did a poor job of supporting DIGEST-MD5
           (as pointed out by Erwan Legrand).  Current version
           tested against a reasonably new Sendmail install.
> 20111130 released r256 to Erwan Legrand (<EMAIL>)
           to evaluate the DIGEST-MD5 changes
* 20111201 fixed TLS issue when SMTP response sent in multiple packets.
           This looks like a regression of the TLS fix from 20050709
           (r94) that was made in the implementation of --pipeline in
           20060309's r115.  Regression reported by Peter J. Holzer along
           with suggested fix.
* 20111202 reimplemented reading of server responses in non-TLS, non-pipe
           sessions in terms of recv rather than as a standard filehandle
           operation (<>) to make it closer to the TLS's read and to allow
           a more in-depth view of what's being sent on the wire.
* 20111202 implemented the -raw/--show-raw-text option to print a hex dump
           of the raw bytes as received on the wire.
> 20111203 released 20111202.0-dev to announce list for review
* 20111209 Fixed default value of DIGEST-MD5's digest-uri to always include
           a "host" value
* 20111209 Allow specifying DIGEST-MD5's digest-uri by extending the -au
           option (USERNAME%URI-SERV-TYPE%URI-HOST%URI-SERV-NAME)
* 20111221 moved --auth-map contents in --dump into the "if auth" section.
* 20111221 added and documented the --auth-extra option.  Initial keywords
           are domain, realm, dmd5-serv-type, dmd5-host, and dmd5-serv-name
* 20111221 removed -au overloading to set DOMAIN in NTLM/MSN/SPA.  Use
           domain --auth-extra keyword now.
* 20111221 removed -au overloading for digest-uri string in DIGEST-MD5. Use
           dmd5-serv-type, dmd5-host, and dmd5-serv-name --auth-extra
           keywords now.
* 20111221 added ability to set realm in the DIGEST-MD5 authenticator using
           realm --auth-extra keyword.
* 20111221 fix --auth-map parsing to accept documented format.
* 20111222 fix handling of --auth-extra dmd5-serv-name to work around lack
           of interface for it in Authen::SASL.
* 20111228 updating contact links at top and adding Id keyword
* 20111229 Change indent from 2-space to 1-tab, use 120-char width where it
           makes sense.
* 20111229 address some -w output related to the DIGEST-MD5 rewrite
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>v********.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-********.0.tar.gz">distribution</a>,
                <a href="./files/swaks-********.0/swaks">script only</a>,
                <a href="./files/swaks-********.0/doc/ref.txt">version docs</a>
            </p>
            <p>
                <b>Change Summary:</b>
            </p>
            <pre><code>
* Completely rewritten documentation.  Designed to sort options by
  broad category to make finding options easier
* New configuration system.  Allow default .swaksrc, custom config
  file, use of swaks itself as a config file, and environment
  variables.
* New option --copy-routing
* New option --auth-optional-strict
* New option --tls-optional-strict
* New option --hide-all
* New option --hide-informational
* New option --tls-get-peer-cert
* New option --attach-name
* Many bugs fixes and other minor tweaks
            </code></pre>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* ******** ******** in --h-Header construct, allow trailing colon and
           allow single dash in addition to double (previously a colon
           in the arg name resulted in double colon in header and
           a single dash resulted in a HELO string of "-Header"
* ******** ******** --header and -h-Header args didn't work for
           replacing existing headers when DATA read in from file.
           Fixed (expected \\n, not \n), though adding new headers
           to the same messages is still broken
* ******** updated copyright year through 2007
* ******** ******** getlogin() is unreliable (see difference in
           auto-from between "swaks -t foo" and
           "echo bar | swaks -t foo") when su'd to another user.
           Replace getlogin() w/ $LOGNAME.  The user can set this
           to whatever they want to be known as, and if it's not
           set we still fall back on getpwuid
* ******** ******** added and documented --copy-routing option
           to allow routing information for a domain that is unrelated
           to the --to address (saves the step of having to look
           up and specify MX records manually using --server).  Arg
           can be domain, @domain, or lp@domain, where domain can be
           a domain name, [*******], or #********.
* ******** DIGEST-MD5 was broken.  Fixed.  (Broke when --pipeline
           was implemented.  When underlying transaction code was
           rewritten, the piece that treats a send string of
           undef and "" differently was lost.  Never noticed because
           only DIGEST-MD5 sends a blank line as part of the protocol.)
* ******** added framework for --auth-optional-strict, needs tweaking
           and docs still
* ******** finished --auth-optional-strict impl incl test frame
* ******** implemented --tls-option-strict
> ******** released ********.0-dev to haraldme.gmail.com
* ******** rearranged dependency processing for code readability
* ******** ******** added --config.  Added new config file and
           environment variable processing to allow multiple,
           ways to specifying options.  Debian bug 403208
> ******** released ********.0-dev to madduck.debian.org, db403208
* 20071016 fixed --tlsos/-tlsos type in POD (haraldme.gmail.com)
* 20071214 removed --input-file and all references to it from
           documentation.  Functionality will be removed in future
           release.
* 20071214 completely reworking documentation.  splitting options
           into tiers based on types of functionality, driving
           requirements into individual sections, and generally
           reorganizing to provide more information while also
           making it more readable.
* ******** deprecated -m, tis a silly option
* 20071220 in config, changed flags from ARGS to OPTIONS.
           changed option/arg split to be a single space - leading
           space on arg is preserved if present.
           changed so that dash(es) on option names are optional.
* 20071220 changed the env var namespace from SWAKS_ARG_ to
           SWAKS_OPT_.  fixed to replace _ w/ - in var names.
* 20071220 Added banner as synonym for CONNECT
           CONNECT behavior that closes the connection without
           sending quit immediately after opening it.  Made -q TLS
           work after tls-on-connect.
* 20071221 made --server/--pipe/--socket mutually exclusive
* 20071227 implemented --hide-all and --hide-informational
* 20071231 changed --silent to accept an arg in addition to being
           additive.  the additive form is now deprecated
* 20071231 fixed long standing implementation bug in the inter-op
           of the --dump and --silent option.
* 20080103 updated copyright to 2008. changed $p_cp formatting to
           remove backslash from email
* 20080103 added yet another layer of abstraction to option processing
* 20080103 implemented recognition of "no-" prefix to erase previously
           set options
* 20080104 fixed --no- processing to not break processing of valid
           options which already begin with "no-"
* 20080104 fixed very obscure bug which caused crash if _no_ option
           processing was done (introduced sometime during the
           introduction of env/conf file opts I think)
* 20080104 if --data has no newlines and represents an openable file,
           use the contents of the file as the DATA
* 20080208 20071221 added recognition of :port suffix to --server
* 20080208 20071220 added ability for config file data to be read
           from __DATA__ if present (portable user defaults)
* 20080208 ******** --add-header now inserts contents at end of
           existing headers if no %H tokens in DATA (allows adding
           headers to non-default messages read from files)
* 20080208 not sure if this was a bug or a misfeature, but removed
           the code that prevent the same header from being changed
           multiple times using --header option
> 20080417 released 20080208.2-dev to Chris Pimlott <chris#pimlott.net>
* 20080618 added post-startup routine for swaks specific tls code
           (keep swaks stuff out of start_tls() but also have common
           place for code shared between -tls and -tlsc
* 20080618 added peer subject DN to TLS information
* 20080618 add --tls-get-peer-cert to save peer cert for evaluation
           (suggested by Martin A. Brooks <martin#antibodymx.net>)
> 20080618 released 20080618.0-dev to martin#antibodymx.net
* 20100203 updated copyright date to 2010
* 20100203 declaring docs good enough, finished rewrite
* 20100204 fixed TZ bug which could occasionally generate TZs offsets
           like +0099 (instead of +0100) for GMT+X TZs (not GMT-X).
           fixed Debian bug 566013)
* 20100204 removed requirement for OPTIONS markers in config files
* 20100204 set a name for attachments based off of filename
* 20100204 added --attach-name option to allow setting of custom
           name or setting that no name should be used.  Need
           pointed out by Stefan Lischke, Christoph Kiechle, and
           Cliff Clasen.
* ******** fixed interaction bug between POD/__END__/&DATA
* ******** working on release engineering
* ******** moved to swaks.org, redid website
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>20061116.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20061116.0">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20060622 Updated FSF address in GPL notice
* 20060623 added url to CONTACT section of docs
* 20060718 20050625 added --protocol option.  'esmtp' is default with
           legacy behavior.  'smtp' only sends HELO, not EHLO.
* 20060718 20050605 added 'lmtp' as a valid protocol
* 20060719 documented all --quit-after synonyms, including new lhlo
           based ones.
* 20060719 documented --protocol option and args, including new
           lmtp protocol
* 20060719 fixed tls/quit-during-second-helo bug introduced by
           addition of --protocol
* 20060719 20050709 TLS options now work on --pipe connections
* 20060724 added and tested local base64 replacements, but haven't
           hooked them in yet (waiting for requirement rewrite)
* 20060803 added and tested load(), but haven't hooked in yet
           (part of requirement rewrite)
* 20060804 20060201 rewrote module support.  Capabilities are checked
           by labels, not with literal modules.  Allows checking
           in main code not to change when behind the scenes
           implementations change.  added avail() and avail_err(),
           test_support(), removed load_modules(), try_load().  Also
           some speed gains because program now only loads modules
           needed for specific invocation - previously preloaded
           every available module, regardless of need.
* 20060804 moved all other modules to conditionally load, as needed.
           This is IO::Socket, IPC::Open2, Time::Local,
           Sys::Hostname, and Getopt::Long.  Time::Local and
           Sys::Hostname are optional (they are not always used,
           and can now be worked around if not available).  IO::Socket
           is now only loaded is doing a socket connection and
           IPC::Open2 only needed for a --pipe session.
* 20060804 added test_support() call to --dump output for testing
* 20060804 changed all base64 calls to use local functions if
           MIME::Base64 isn't available.  Module is still preferred
           due to "many eyes" and speed.  Removed MIME::Base64 from
           auth requirements.
* 20060804 didn't realize eb64 needed to support line endings because
           of use in encoding mime bodies.  Add in feature.
* 20060804 fixed get_date_string() to return a true GMT string, not
           the local time w/ a GMT stamp if date_manip unavailable
* 20060804 redid the REQ docs to reflect current requirements
           and behaviors.
* 20060817 redid --support/avail() again to support concept of
           optional (optimizing) modules for full disclosure in output
* 20060914 added --header option which, if header already exists,
           overwrites it.  Otherwise adds to --add-header processing.
* 20060914 added --header-HEADER option where --h-Text foo is
           a synonym for --header "Text: foo"
* 20060914 removed some extraneous /e opts from body-processing regexps
* 20061010 made AUTH auth_types case insensitive as required in
           rfc2554.  Debian bug 392182
* 20061010 fixed bug time bomb with handling legacy AUTH= ESMTP
           lines noticed while addressing the above.
* 20061010 20060914 documented --header and --h-Header options
           (though badly.  I really need to redo the docs)
* 20061011 20061010 --port can now be a service name as well as
           a port number.  Internally, now tries resolve 'smtp' and
           'smtps' services as default ports reverting to old 25/465
           behavior if those aren't found
* 20061018 set default port for LMTP to 'lmtp'/24 and documented
* 20061018 implemented groundwork for overloading --protocol with
           broad protocol types.  implemented overload->simple
           protocol mapping, still missing TLS and AUTH mapping
* 20061023 finished implementing protocol overloading (tls and auth)
* 20061115 documented rfc3848 --protocol options and behaviors
* 20061115 20060806 fixed bug where From: was prompted for on some -q
           types that don't require it, too (Helo: too)
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>20060621.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20060621.0">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20060121 updated copyright to 2006
* 20060121 fiddled w/ --dump output, trying to get every possible
           option displayed (for test suite)
* 20060121 commented out --auth-hide-password option since we aren't
           handling it anyway.  Was undocumented also.
* 20060131 fixed bug in checking for Authen::DigestMD5 when
           trying DIGEST-MD5 auth type (reported by jh.plonk.de)
* 20060201 simplified get_digest() code and allowed different
           digest types to be used
* 20060201 implemented CRAM-SHA1 auth type (suggested jh.plonk.de)
* 20060201 20050909 can now specify -a as a comma delimited list of ok
           types to try, in order of preference
* 20060201 fixed --dump to handle new auth type format
* 20060201 20050909 fixed stupid message 'ANY authentication not
           supported' if no auth type is specified and the server
           doesn't advertise auth
* 20060201 20050721 changed auto sender address generation to try
           to get lowest logged-in user name for the local_part.
           If that method fails, use old method of looking up $<
* 20060201 added --force-getpwuid option to force old method of
           looking up $< to generate sender local part
* 20060201 20050810 fixed stupid inefficiency where both do_smtp_rcpt()
           and caller of do_smtp_rcpt() are handling comma-delim
           recipient list.
* 20060210 20060131 change --pipe to use open2().  This causes child
           stderr not to be handled by swaks - user can redirect
           as desired.  Fixed bug noted by jh.plonk.de
> 20060210 released 20060210.0-dev to jh.plonk.de
* 20060218 swaks choked if server advertised STARTTLS over a
           --tls-on-connect connection.  fixed.  (jpeacock.rowman.com)
* 20060221 20051102 despite some error messages to the contrary,
           swaks did not handle the remote end closing its
           connection unexpectedly.  Fixed transaction routines
           to detect and handle properly.
* 20060221 with io handling on broken connections working better,
           remove the CHLD and PIPE signal handlers so --pipe behaves
           more like sockets
* 20060221 misc code tidying
* 20060221 20060130 if quitting before rcpt and link type is --socket
           or --pipe, do not prompt for recipient.
> 20060221 released 20060221.4-dev to jh.plonk.de
* 20060222 swaks died on SIGPIPE on unexpected disconnect over
           --socket.  fixed.
* 20060307 20050510 added simple implementation of --body, --attach,
           and --attach-type options (Debian bug 354084)
* 20060308 --attach/--body tweaks (add closing boundary in mime,
           added ability to use - for multiple body/attach parts,
           allowed --body and --attach to coexist, added docs
           for the three options)
* 20060309 20050406 added --pipeline option
> 20060309 released 20060309.0-dev to exim-users.exim.org
* 20060526 removed old (pre-pipeline) transaction code
* 20060526 added and document --add-header option (Debian bug 366317)
* 20060621 added comments to top of code about viewing docs
* 20060621 added note at top and in docs about update email address
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>20050709.1</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20050709.1">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20050629 fixed bug where latent $@ errors caused socket connections
           to seem to fail (run --socket on a server w/o Net::DNS)
* 20050709 fixed bug where TLS multiline responses were broken if
           sent in multiple packets (reported by
           charlieb.budge.apana.org.au)
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>20050625.8</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20050625.8">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20050625 fixed bug in do_smtp_tls where it ignored timeout on
           on STARTTLS call
* 20050625 fixed bug where <> not correctly handled for user/pass
* 20050625 cleaned up some unused global vars
* 20050625 moved socket from passed var to part of global link struct
* 20050625 moved tls info from disconnected global vars to link struct
* 20050625 cleaned up lots of orphaned code and trimmed socket passing
* 20050625 moved timeout into link struct and cleaned up code
* 20050625 saved many lines of code by adding do_smtp_quit
* 20050625 moved all connection level into into %G::link very early
* 20050625 20050517 added --pipe option to be able to conduct
           smtp transaction with a child process.  See Debian
           bug ID 309462
* 20050625 fixed a couple of bugs in --auth-optional (one caused it
           not to work at all, the other caused it not to work if
           either -a not also used or some arg not supplied)
* 20050625 20050605 added --socket option to allow smtp over
           unix domain socket
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>20050605.3</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20050605.3">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20050605 updated copyright dates
* 20050605 20050510 added OS test on getpwuid to allow running on
           Win32, spent some time evaluating capabilities (reported
           alexander.hass#e-domizil.de)
* 20050605 added portability section to documentation
* 20060605 20040909 the line ending translation was very slow for
           large files.  fixed.
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>20040404.1</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20040404.1">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20040404 added --no-data-fixup option to allow a literal DATA
           value to be passed in
* 20040404 by default, translate all bare newlines to CRLF.  Fixed
           Debian bug 241368
* 20040404 updated copyright dates
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>20040128.1</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20040128.1">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20031226 hostname verification broke FROM and HELO options in -l
           file.  fixed.
* 20040115 fixed some badly formatted POD
* 20040115 added --dump option (show generated options but don't send)
* 20040115 couldn't use -ao to define specific auth type - fixed
* 20040128 fixed doc bug re: default DATA value (sugg. ametzler)
* 20040128 added new quit type of 'first-helo' (for the first helo
           in a STARTTLS session).  Changed HELO type to be second
           helo in a TLS session instead of first.
* 20040128 added error checking for -q opt - error out if unknown val.
           (sugg. ametzler)
* 20040128 added a few -q aliases (ehlo, first-ehlo, starttls, from, to)
* 20040128 if -q opt is one which doesn't require a to address
           (everything before rcpt) and server is otherwise specified,
           don't require a to address. (sugg. ametzler)
* 20040128 reworked a split to quell a perl -w gripe
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>20031218.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20031218.0">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20031212 added --tls-on-connect option to support smtps (suggested
           by Benjamin Ritcey &lt;exim#ritcey.com&gt;)
* 20031212 -tlsc implementation uncovered deficiencies in error
           checking in start_tls().  reworked.
* 20031212 print SSL error code if negotiation failed
* 20031212 oops.  fixed so that -tlsc doesn't cause double EHLO
* 20031212 Added X-Mailer header to default DATA.
* 20031212 in some situations a hostname may not be found automatically
           (for instance, on my laptop when my VPN is active).  In this
           situation, prompt for helo string and from address.
* 20031218 reworked local hostname determination.  hostname() almost
           never fails but gethostbyname can, use first results if
           second call fails.
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>20031211.2</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-20031211.2">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* 20031211 the subscripting in the date routine was incorrect, failed
           to compile on older perls
* 20031211 removed -U and -F options from the perldoc call for --help.
           (unknown by older perls
* 20031211 since perldoc didn't want to run as root anyway I caught
           that case and printed a more informative message
* 20031211 duh.  if --help run as root, just change to uid 1 before
           running perldoc.  no fuss no muss
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>********.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/swaks-********.0">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* ******** changed name from vmail to swaks (SWiss Army Knife Smtp)
* ******** changed copyright/license to GPL
* ******** changed %D body token to be compliant for an RFC Date: header
* ******** add 'Date: %D' header to default data string (probably
           should have been there all along but this was specifically
           added to address a change in the handling of messages
           without Date: headers by exim in 4.30)
* ******** handle default routing correctly for domain literal
           to addresses (@[*******]).  Had to specify server explicitly
           using -s before.
* ******** moved "Trying..." above actual socket creation, which
           makes much more sense in a timeout situation
* ******** implemented automatic routing of RFC821 decimal domain
           literals (user@#********).
            </code></pre>
        </div>
    </div>

    <div class="row">
        <div class="two columns">
            <b>********.0</b>
        </div>
        <div class="ten columns">
            <p>
                <b>Links:</b>
                <a href="./files/vmail.********.0">script only</a>
            </p>
            <p>
                <b>Changelog:</b>
            </p>
            <pre><code>
* ******** initial implementation
* ******** added ability to specify pieces to be prompted for
           interactively. trumps both file and CL args (-i)
* ******** altered transaction hints (--&gt;, &lt;--) to make more clear
* ******** added %D token for body
* ******** added smart default for helo (get_hostname())
* ******** added smart default for from
* ******** added smart server lookup based off of to address
* ******** changed so it prompts for to if one not supplied (instead
           of erroring and dying)
* 20020116 added -g option to allow DATA to be read from STDIN
* 20020201 rewrote to use IO::Socket instead of LWP::Socket
* 20021108 removed nslookup refs and implemented Net::DNS to get MX
* 20021108 added -m option to force emulation of 'Mail -v'
* 20021112 added -q option to force premature 'quit'.  it accepts
           'connect', 'helo', 'mail', 'rcpt' as params and sends quit
           immediately following the named transaction.
* 20021126 removed the old, commented LWP::Socket code.
* 20021126 started cleaning up transaction code a bit
* 20021126 finished implementation of transact.
* 20021126 removed references to WCS::Util to make more portable
* 20021126 if server isn't specified and Net::DNS not installed, use
           localhost as mail server (portability)
* 20030204 added -n option to summarize DATA send instead of sending
           whole thing (more useful when sending real (and possibly
           large) emails w/ the -g option).
* 20030306 reworked transaction architecture so more features can be
           added in the future
* 20030306 exit status changes on unexpected occurrences now
* 20030306 added code to allow multiline SMTP responses
* 20030306 use '***' to flag unexpected SMTP responses
* 20030306 closes the connection politely if something unexpected happens
* 20030306 added ability to specify multiple recipients
* 20030306 changed option processing from ::Std to ::Long to allow
           for future expansion
* 20030306 changed HELO code to try EHLO, fall back to HELO
* 20030306 fixed bug in just sending \n instead of \r\n (affected
           mx01.guardent.com (PIX), though not our servers)
* 20030306 did away w/ -i option.  Now the arg of those opts is
           optional - if the arg is given an opt, it is used.  if
           arg is given w/o opt, will prompt on STDIN.  If no arg given
           use default (mostly, depends on opt
* 20030307 if -l file can't be processed, print error and fall
           back to other methods
* 20030307 changed -l file processing to simply populate a hash so
           whole function doesn't have to be changed each time opt
           added
* 20030307 added 'auth' as valid value for -q
* 20030307 added basic AUTH for PLAIN
* 20030308 added basic AUTH for LOGIN
* 20030308 added basic AUTH for CRAM-MD5
* 20030309 fixed to try multiple auth type or specific w/ -a &lt;type&gt;
* 20030312 fixed base64 encoding bug (would wrap after ~58 chars)
           (has existed entire implementation)
* 20030312 added -li opt to specify local interface
* 20030312 changed error on socket creation to use print_transaction
* 20030312 added -apt opt to translate all base64 strings to plaintext
* 20030317 fixed AUTH LOGIN bug on null password (has existed for
           entire implementation)
* 20030317 changed -ap to accept 'NULLNULL' to mean empty password
           from command line (otherwise you couldn't script it)
* 20030317 changed -au to accept 'NULLNULL' to mean empty password
           from command line (otherwise you couldn't script it)
* 20030324 changed module handling to load all up front
* 20030324 added --support option to determine capabilities
* 20030326 fixed typo on testing for Net::DNS loading (since 3.24)
* 20030326 fixed error where one too many newlines being added after
           trailing . on -g input.  caused a mailer error (extra
           newline seen as an unknown command
* 20030410 ugh.  fixed problem w/ default data now having two ending
           dots.
* 20030414 laid groundwork for adding TLS (option processing)
* 20030415 added basic TLS support w/ Net::SSLeay
* 20030424 allow null sender (-f NULLNULL)
* 20030826 allow mult recips in form to,to,to (if server is undef,
           uses DNS for domain of last recip in list)
* 20031008 add -to option to allow timeout to be specified in seconds
           on command line. (defaults to 30s)
* 20031008 fixed bug where alarm was not reset after
* 20031008 allow TIMEOUT to be specified in -l file
* 20031008 changed so you can specify in time format (hms).  This
           allows you to use 0s to mean no timeout
* 20031008 adjusted Getopt::Long to have case-sensitive single
           char options, but insensitive long opts.  It also turns
           off single-char option bundling, but I don't see this
           as an issue
* 20031008 instead of passing literal prefix to print_transaction(),
           now pass a code (smtp/program/send/receive/ok/error) and
           the sub prints the appropriate prefix.  Allows prefixes
           to be controlled in a central place
* 20031008 changed prefixes so that program errors are differentiated
           from SMTP errors (*** vs. &lt;** or **&gt;)
* 20031008 changed standard diagnostic prints to use print_trans()
           to gain benefit of standard prefixes
* 20031008 standardized printing of program errors to STDERR, all
           other prints to STDOUT
* 20031008 added -S option. -S only prints errors and anything after,
           -S -S only shows errors, -S -S -S never prints anything
* 20031010 DATA portion now has From_ line removed if present
* 20031010 added -nsf (no strip from) option to prevent removal
           of From_ line
* 20031010 added transaction hints for TLS (&lt;~ and ~&gt;)
* 20031010 making TLS confirm to RFC3207 - forget state information
           and re-EHLO after successful TLS negotiation
* 20031010 fixed bug where QUIT not sent if -tls specified but TLS not
           advertised by the server
* 20031010 added 'tls' as valid argument to -q option
* 20031010 -ahp and -apt were defined as string options, but they're
           really boolean, changed definition
* 20031010 removed most of the input validation - this is meant to be
           a test app, not a real agent.  If an admin wants to test
           how his server will handle an underscore in a helo string,
           he should be able to specify it
* 20031010 added %D to the default subject
* 20031010 fix dots once and for all - honor a trailing dot if it
           exists, otherwise add trailing dot.  Quote all other dots
* 20031010 added framework for release (--help, --version), initial
           population of help framework
* 20031014 changed timeout CLA from -to to -timeout to avoid confusion
* 20031014 added longer alternatives to most options (that is, -ao
           can also be specified as --auth-optional
* 20031016 typo - changed --supress-data to --suppress-data
* 20031016 documented long options in POD
* 20031016 moved TLS code into sub (mimic AUTH architecture)
* 20031016 arg of '-' to -d sets -g (reads from STDIN)
* 20031019 Added -nth option to turn off transaction hints
* 20031019 added -hr and -hs options to mute parts of transaction
* 20031019 cleaned up getopt code (messy since adding long opts
* 20031019 started adding NTLM support (doesn't work totally yet)
* 20031020 NTLM basically works.  Fixed first problem by changing
           expected return value from 334 to 235 in getting final auth
           verdict.  Still issue w/ username (but pass works fine)
* 20031020 switched to alternate Authen::NTLM module to fix username
           issue (seems to work so far but might be safer to switch to
           native implementation)
* 20031021 documented NTLM auth options
* 20031021 added ability to specify %DOMAIN to -ap for NTLM
* 20031021 Added aliases for SPA/MSN to NTLM
* 20031024 started rearranging options.  Move from $O:: namespace
           to %O hash.  %O is for temporary vars, $G:: is for true
           global vars.  moved most of the up-front processing to
           a subroutine and rearranged
* 20031024 Added module checking for Authen::NTLM
* 20031024 changed server for non-mx domain from localhost to domain's
           A record
* 20031024 changed "empty" placeholder from NULLNULL to &lt;&gt; for
           sender, a_user, a_pass
* 20031027 added -stl option to show lapse between send/receive
* 20031101 fixed bug in NTLM auth (worked with exim but not with
           communigate
* 20031107 implemented DIGEST-MD5 (RFC2831)
* 20031107 fixed a typo in NTLM doc
* 20031107 fixed -apt bug in NTLM exchange
* 20031107 added require section to --help
* 20031108 changed -stl to a microtimer by default
* 20031108 changed microtimer precision to thousandths
* 20031108 added back in integer timing if 'i' provided as arg to -stl
           or Time::HiRes unavailable
* 20031108 added -am option to allow aliasing of auth types
* 20031108 reworked do_smtp_auth() to make --auth-map possible and for
           general streamlining
* 20031108 missed a piece in implementing DIGEST-MD5 (missed checking
           for Authen::DigestMD5)
            </code></pre>
        </div>
    </div>

</div> <!-- end of content container -->

<div class="section footer">
    <div class="container">
        <div class="row">
            <hr/>
            <p><a href="/john/">www.jetmore.org/john</a> / <a href="/john/code/">code</a> / <a href="/john/code/swaks/">swaks</a> / <b>versions</b></p>
        </div>
    </div>
</div>

</body>
</html>
