# DO NOT EDIT THIS FILE. EDIT THE MAIN.CF FILE INSTEAD. THE
# TEXT HERE JUST SHOWS DEFAULT SETTINGS BUILT INTO POSTFIX.
#
2bounce_notice_recipient = postmaster
access_map_defer_code = 450
access_map_reject_code = 554
address_verify_cache_cleanup_interval = 12h
address_verify_default_transport = $default_transport
address_verify_local_transport = $local_transport
address_verify_map = btree:$data_directory/verify_cache
address_verify_negative_cache = yes
address_verify_negative_expire_time = 3d
address_verify_negative_refresh_time = 3h
address_verify_pending_request_limit = 5000
address_verify_poll_count = ${stress?{1}:{3}}
address_verify_poll_delay = 3s
address_verify_positive_expire_time = 31d
address_verify_positive_refresh_time = 7d
address_verify_relay_transport = $relay_transport
address_verify_relayhost = $relayhost
address_verify_sender = $double_bounce_sender
address_verify_sender_dependent_default_transport_maps = $sender_dependent_default_transport_maps
address_verify_sender_dependent_relayhost_maps = $sender_dependent_relayhost_maps
address_verify_sender_ttl = 0s
address_verify_service_name = verify
address_verify_transport_maps = $transport_maps
address_verify_virtual_transport = $virtual_transport
alias_database = hash:/etc/aliases
alias_maps = hash:/etc/aliases, nis:mail.aliases
allow_mail_to_commands = alias, forward
allow_mail_to_files = alias, forward
allow_min_user = no
allow_percent_hack = yes
allow_untrusted_routing = no
alternate_config_directories =
always_add_missing_headers = no
always_bcc =
anvil_rate_time_unit = 60s
anvil_status_update_time = 600s
append_at_myorigin = yes
append_dot_mydomain = ${{$compatibility_level} < {1} ? {yes} : {no}}
application_event_drain_time = 100s
authorized_flush_users = static:anyone
authorized_mailq_users = static:anyone
authorized_submit_users = static:anyone
backwards_bounce_logfile_compatibility = yes
berkeley_db_create_buffer_size = ********
berkeley_db_read_buffer_size = 131072
best_mx_transport =
biff = yes
body_checks =
body_checks_size_limit = 51200
bounce_notice_recipient = postmaster
bounce_queue_lifetime = 5d
bounce_service_name = bounce
bounce_size_limit = 50000
bounce_template_file =
broken_sasl_auth_clients = no
canonical_classes = envelope_sender, envelope_recipient, header_sender, header_recipient
canonical_maps =
cleanup_service_name = cleanup
command_directory = /home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin
command_execution_directory =
command_expansion_filter = 1234567890!@%-_=+:,./abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
command_time_limit = 1000s
compatibility_level = 0
config_directory = /home/<USER>/Documents/git/swaks/testing/mta/postfix-config
confirm_delay_cleared = no
connection_cache_protocol_timeout = 5s
connection_cache_service_name = scache
connection_cache_status_update_time = 600s
connection_cache_ttl_limit = 2s
content_filter =
cyrus_sasl_config_path =
daemon_directory = /home/<USER>/Documents/git/swaks/testing/mta/postfix-install/libexec
daemon_table_open_error_is_fatal = no
daemon_timeout = 18000s
data_directory = /home/<USER>/Documents/git/swaks/testing/mta/postfix-install/lib
debug_peer_level = 2
debug_peer_list =
debugger_command =
default_database_type = hash
default_delivery_slot_cost = 5
default_delivery_slot_discount = 50
default_delivery_slot_loan = 3
default_delivery_status_filter =
default_destination_concurrency_failed_cohort_limit = 1
default_destination_concurrency_limit = 20
default_destination_concurrency_negative_feedback = 1
default_destination_concurrency_positive_feedback = 1
default_destination_rate_delay = 0s
default_destination_recipient_limit = 50
default_extra_recipient_limit = 1000
default_filter_nexthop =
default_minimum_delivery_slots = 3
default_privs = nobody
default_process_limit = 100
default_rbl_reply = $rbl_code Service unavailable; $rbl_class [$rbl_what] blocked using $rbl_domain${rbl_reason?; $rbl_reason}
default_recipient_limit = 20000
default_recipient_refill_delay = 5s
default_recipient_refill_limit = 100
default_transport = smtp
default_transport_rate_delay = 0s
default_verp_delimiters = +=
defer_code = 450
defer_service_name = defer
defer_transports =
delay_logging_resolution_limit = 2
delay_notice_recipient = postmaster
delay_warning_time = 0h
deliver_lock_attempts = 20
deliver_lock_delay = 1s
destination_concurrency_feedback_debug = no
detect_8bit_encoding_header = yes
disable_dns_lookups = no
disable_mime_input_processing = no
disable_mime_output_conversion = no
disable_verp_bounces = no
disable_vrfy_command = no
dns_ncache_ttl_fix_enable = no
dnsblog_reply_delay = 0s
dnsblog_service_name = dnsblog
dont_remove = 0
double_bounce_sender = double-bounce
duplicate_filter_limit = 1000
empty_address_default_transport_maps_lookup_key = <>
empty_address_recipient = MAILER-DAEMON
empty_address_relayhost_maps_lookup_key = <>
enable_idna2003_compatibility = no
enable_long_queue_ids = no
enable_original_recipient = yes
error_delivery_slot_cost = $default_delivery_slot_cost
error_delivery_slot_discount = $default_delivery_slot_discount
error_delivery_slot_loan = $default_delivery_slot_loan
error_destination_concurrency_failed_cohort_limit = $default_destination_concurrency_failed_cohort_limit
error_destination_concurrency_limit = $default_destination_concurrency_limit
error_destination_concurrency_negative_feedback = $default_destination_concurrency_negative_feedback
error_destination_concurrency_positive_feedback = $default_destination_concurrency_positive_feedback
error_destination_rate_delay = $default_destination_rate_delay
error_destination_recipient_limit = $default_destination_recipient_limit
error_extra_recipient_limit = $default_extra_recipient_limit
error_initial_destination_concurrency = $initial_destination_concurrency
error_minimum_delivery_slots = $default_minimum_delivery_slots
error_notice_recipient = postmaster
error_recipient_limit = $default_recipient_limit
error_recipient_refill_delay = $default_recipient_refill_delay
error_recipient_refill_limit = $default_recipient_refill_limit
error_service_name = error
error_transport_rate_delay = $default_transport_rate_delay
execution_directory_expansion_filter = 1234567890!@%-_=+:,./abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
expand_owner_alias = no
export_environment = TZ MAIL_CONFIG LANG
fallback_transport =
fallback_transport_maps =
fast_flush_domains = $relay_domains
fast_flush_purge_time = 7d
fast_flush_refresh_time = 12h
fault_injection_code = 0
flush_service_name = flush
fork_attempts = 5
fork_delay = 1s
forward_expansion_filter = 1234567890!@%-_=+:,./abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
forward_path = $home/.forward${recipient_delimiter}${extension}, $home/.forward
frozen_delivered_to = yes
hash_queue_depth = 1
hash_queue_names = deferred, defer
header_address_token_limit = 10240
header_checks =
header_from_format = standard
header_size_limit = 102400
helpful_warnings = yes
home_mailbox =
hopcount_limit = 50
html_directory = no
ignore_mx_lookup_error = no
import_environment = MAIL_CONFIG MAIL_DEBUG MAIL_LOGTAG TZ XAUTHORITY DISPLAY LANG=C POSTLOG_SERVICE POSTLOG_HOSTNAME
in_flow_delay = 1s
inet_interfaces = all
inet_protocols = all
info_log_address_format = external
initial_destination_concurrency = 5
internal_mail_filter_classes =
invalid_hostname_reject_code = 501
ipc_idle = 5s
ipc_timeout = 3600s
ipc_ttl = 1000s
line_length_limit = 2048
lmdb_map_size = ********
lmtp_address_preference = any
lmtp_address_verify_target = rcpt
lmtp_assume_final = no
lmtp_balance_inet_protocols = yes
lmtp_bind_address =
lmtp_bind_address6 =
lmtp_body_checks =
lmtp_cname_overrides_servername = no
lmtp_connect_timeout = 0s
lmtp_connection_cache_destinations =
lmtp_connection_cache_on_demand = yes
lmtp_connection_cache_time_limit = 2s
lmtp_connection_reuse_count_limit = 0
lmtp_connection_reuse_time_limit = 300s
lmtp_data_done_timeout = 600s
lmtp_data_init_timeout = 120s
lmtp_data_xfer_timeout = 180s
lmtp_defer_if_no_mx_address_found = no
lmtp_delivery_slot_cost = $default_delivery_slot_cost
lmtp_delivery_slot_discount = $default_delivery_slot_discount
lmtp_delivery_slot_loan = $default_delivery_slot_loan
lmtp_delivery_status_filter = $default_delivery_status_filter
lmtp_destination_concurrency_failed_cohort_limit = $default_destination_concurrency_failed_cohort_limit
lmtp_destination_concurrency_limit = $default_destination_concurrency_limit
lmtp_destination_concurrency_negative_feedback = $default_destination_concurrency_negative_feedback
lmtp_destination_concurrency_positive_feedback = $default_destination_concurrency_positive_feedback
lmtp_destination_rate_delay = $default_destination_rate_delay
lmtp_destination_recipient_limit = $default_destination_recipient_limit
lmtp_discard_lhlo_keyword_address_maps =
lmtp_discard_lhlo_keywords =
lmtp_dns_reply_filter =
lmtp_dns_resolver_options =
lmtp_dns_support_level =
lmtp_enforce_tls = no
lmtp_extra_recipient_limit = $default_extra_recipient_limit
lmtp_fallback_relay =
lmtp_generic_maps =
lmtp_header_checks =
lmtp_host_lookup = dns
lmtp_initial_destination_concurrency = $initial_destination_concurrency
lmtp_lhlo_name = $myhostname
lmtp_lhlo_timeout = 300s
lmtp_line_length_limit = 998
lmtp_mail_timeout = 300s
lmtp_mime_header_checks =
lmtp_minimum_delivery_slots = $default_minimum_delivery_slots
lmtp_mx_address_limit = 5
lmtp_mx_session_limit = 2
lmtp_nested_header_checks =
lmtp_per_record_deadline = no
lmtp_pix_workaround_delay_time = 10s
lmtp_pix_workaround_maps =
lmtp_pix_workaround_threshold_time = 500s
lmtp_pix_workarounds = disable_esmtp,delay_dotcrlf
lmtp_quit_timeout = 300s
lmtp_quote_rfc821_envelope = yes
lmtp_randomize_addresses = yes
lmtp_rcpt_timeout = 300s
lmtp_recipient_limit = $default_recipient_limit
lmtp_recipient_refill_delay = $default_recipient_refill_delay
lmtp_recipient_refill_limit = $default_recipient_refill_limit
lmtp_reply_filter =
lmtp_rset_timeout = 20s
lmtp_sasl_auth_cache_name =
lmtp_sasl_auth_cache_time = 90d
lmtp_sasl_auth_enable = no
lmtp_sasl_auth_soft_bounce = yes
lmtp_sasl_mechanism_filter =
lmtp_sasl_password_maps =
lmtp_sasl_path =
lmtp_sasl_security_options = noplaintext, noanonymous
lmtp_sasl_tls_security_options = $lmtp_sasl_security_options
lmtp_sasl_tls_verified_security_options = $lmtp_sasl_tls_security_options
lmtp_sasl_type = cyrus
lmtp_send_dummy_mail_auth = no
lmtp_send_xforward_command = no
lmtp_sender_dependent_authentication = no
lmtp_skip_5xx_greeting = yes
lmtp_skip_quit_response = no
lmtp_starttls_timeout = 300s
lmtp_tcp_port = 24
lmtp_tls_CAfile =
lmtp_tls_CApath =
lmtp_tls_block_early_mail_reply = no
lmtp_tls_cert_file =
lmtp_tls_chain_files =
lmtp_tls_ciphers = medium
lmtp_tls_connection_reuse = no
lmtp_tls_dcert_file =
lmtp_tls_dkey_file = $lmtp_tls_dcert_file
lmtp_tls_eccert_file =
lmtp_tls_eckey_file = $lmtp_tls_eccert_file
lmtp_tls_enforce_peername = yes
lmtp_tls_exclude_ciphers =
lmtp_tls_fingerprint_cert_match =
lmtp_tls_fingerprint_digest = md5
lmtp_tls_force_insecure_host_tlsa_lookup = no
lmtp_tls_key_file = $lmtp_tls_cert_file
lmtp_tls_loglevel = 0
lmtp_tls_mandatory_ciphers = medium
lmtp_tls_mandatory_exclude_ciphers =
lmtp_tls_mandatory_protocols = !SSLv2, !SSLv3
lmtp_tls_note_starttls_offer = no
lmtp_tls_per_site =
lmtp_tls_policy_maps =
lmtp_tls_protocols = !SSLv2, !SSLv3
lmtp_tls_scert_verifydepth = 9
lmtp_tls_secure_cert_match = nexthop
lmtp_tls_security_level =
lmtp_tls_servername =
lmtp_tls_session_cache_database =
lmtp_tls_session_cache_timeout = 3600s
lmtp_tls_trust_anchor_file =
lmtp_tls_verify_cert_match = hostname
lmtp_tls_wrappermode = no
lmtp_transport_rate_delay = $default_transport_rate_delay
lmtp_use_tls = no
lmtp_xforward_timeout = 300s
local_command_shell =
local_delivery_slot_cost = $default_delivery_slot_cost
local_delivery_slot_discount = $default_delivery_slot_discount
local_delivery_slot_loan = $default_delivery_slot_loan
local_delivery_status_filter = $default_delivery_status_filter
local_destination_concurrency_failed_cohort_limit = $default_destination_concurrency_failed_cohort_limit
local_destination_concurrency_limit = 2
local_destination_concurrency_negative_feedback = $default_destination_concurrency_negative_feedback
local_destination_concurrency_positive_feedback = $default_destination_concurrency_positive_feedback
local_destination_rate_delay = $default_destination_rate_delay
local_destination_recipient_limit = 1
local_extra_recipient_limit = $default_extra_recipient_limit
local_header_rewrite_clients = permit_inet_interfaces
local_initial_destination_concurrency = $initial_destination_concurrency
local_minimum_delivery_slots = $default_minimum_delivery_slots
local_recipient_limit = $default_recipient_limit
local_recipient_maps = proxy:unix:passwd.byname $alias_maps
local_recipient_refill_delay = $default_recipient_refill_delay
local_recipient_refill_limit = $default_recipient_refill_limit
local_transport = local:$myhostname
local_transport_rate_delay = $default_transport_rate_delay
luser_relay =
mail_name = Postfix
mail_owner = postfix
mail_release_date = 20201107
mail_spool_directory = /home/<USER>/Documents/git/swaks/testing/mta/postfix-install/mailspool
mail_version = 3.5.8
mailbox_command =
mailbox_command_maps =
mailbox_delivery_lock = fcntl, dotlock
mailbox_size_limit = 51200000
mailbox_transport =
mailbox_transport_maps =
maillog_file =
maillog_file_compressor = gzip
maillog_file_prefixes = /var, /dev/stdout
maillog_file_rotate_suffix = %Y%m%d-%H%M%S
mailq_path = /home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/mailq
manpage_directory = /home/<USER>/Documents/git/swaks/testing/mta/postfix-install/man
maps_rbl_domains =
maps_rbl_reject_code = 554
masquerade_classes = envelope_sender, header_sender, header_recipient
masquerade_domains =
masquerade_exceptions =
master_service_disable =
max_idle = 100s
max_use = 100
maximal_backoff_time = 4000s
maximal_queue_lifetime = 5d
message_drop_headers = bcc, content-length, resent-bcc, return-path
message_reject_characters =
message_size_limit = 10240000
message_strip_characters =
meta_directory = /home/<USER>/Documents/git/swaks/testing/mta/postfix-config
milter_command_timeout = 30s
milter_connect_macros = j {daemon_name} {daemon_addr} v _
milter_connect_timeout = 30s
milter_content_timeout = 300s
milter_data_macros = i
milter_default_action = tempfail
milter_end_of_data_macros = i
milter_end_of_header_macros = i
milter_header_checks =
milter_helo_macros = {tls_version} {cipher} {cipher_bits} {cert_subject} {cert_issuer}
milter_macro_daemon_name = $myhostname
milter_macro_defaults =
milter_macro_v = $mail_name $mail_version
milter_mail_macros = i {auth_type} {auth_authen} {auth_author} {mail_addr} {mail_host} {mail_mailer}
milter_protocol = 6
milter_rcpt_macros = i {rcpt_addr} {rcpt_host} {rcpt_mailer}
milter_unknown_command_macros =
mime_boundary_length_limit = 2048
mime_header_checks = $header_checks
mime_nesting_limit = 100
minimal_backoff_time = 300s
multi_instance_directories =
multi_instance_enable = no
multi_instance_group =
multi_instance_name =
multi_instance_wrapper =
multi_recipient_bounce_reject_code = 550
mydestination = $myhostname, localhost.$mydomain, localhost
mynetworks_style = ${{$compatibility_level} < {2} ? {subnet} : {host}}
myorigin = $myhostname
nested_header_checks = $header_checks
newaliases_path = /home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/newaliases
non_fqdn_reject_code = 504
non_smtpd_milters =
notify_classes = resource, software
openssl_path = openssl
owner_request_special = yes
parent_domain_matches_subdomains = debug_peer_list,fast_flush_domains,mynetworks,permit_mx_backup_networks,qmqpd_authorized_clients,relay_domains,smtpd_access_maps
permit_mx_backup_networks =
pickup_service_name = pickup
pipe_delivery_status_filter = $default_delivery_status_filter
plaintext_reject_code = 450
postlog_service_name = postlog
postlogd_watchdog_timeout = 10s
postmulti_control_commands = reload flush
postmulti_start_commands = start
postmulti_stop_commands = stop abort drain quick-stop
postscreen_access_list = permit_mynetworks
postscreen_bare_newline_action = ignore
postscreen_bare_newline_enable = no
postscreen_bare_newline_ttl = 30d
postscreen_blacklist_action = ignore
postscreen_cache_cleanup_interval = 12h
postscreen_cache_map = btree:$data_directory/postscreen_cache
postscreen_cache_retention_time = 7d
postscreen_client_connection_count_limit = $smtpd_client_connection_count_limit
postscreen_command_count_limit = 20
postscreen_command_filter =
postscreen_command_time_limit = ${stress?{10}:{300}}s
postscreen_disable_vrfy_command = $disable_vrfy_command
postscreen_discard_ehlo_keyword_address_maps = $smtpd_discard_ehlo_keyword_address_maps
postscreen_discard_ehlo_keywords = $smtpd_discard_ehlo_keywords
postscreen_dnsbl_action = ignore
postscreen_dnsbl_max_ttl = ${postscreen_dnsbl_ttl?{$postscreen_dnsbl_ttl}:{1}}h
postscreen_dnsbl_min_ttl = 60s
postscreen_dnsbl_reply_map =
postscreen_dnsbl_sites =
postscreen_dnsbl_threshold = 1
postscreen_dnsbl_timeout = 10s
postscreen_dnsbl_whitelist_threshold = 0
postscreen_enforce_tls = $smtpd_enforce_tls
postscreen_expansion_filter = $smtpd_expansion_filter
postscreen_forbidden_commands = $smtpd_forbidden_commands
postscreen_greet_action = ignore
postscreen_greet_banner = $smtpd_banner
postscreen_greet_ttl = 1d
postscreen_greet_wait = ${stress?{2}:{6}}s
postscreen_helo_required = $smtpd_helo_required
postscreen_non_smtp_command_action = drop
postscreen_non_smtp_command_enable = no
postscreen_non_smtp_command_ttl = 30d
postscreen_pipelining_action = enforce
postscreen_pipelining_enable = no
postscreen_pipelining_ttl = 30d
postscreen_post_queue_limit = $default_process_limit
postscreen_pre_queue_limit = $default_process_limit
postscreen_reject_footer = $smtpd_reject_footer
postscreen_reject_footer_maps = $smtpd_reject_footer_maps
postscreen_tls_security_level = $smtpd_tls_security_level
postscreen_upstream_proxy_protocol =
postscreen_upstream_proxy_timeout = 5s
postscreen_use_tls = $smtpd_use_tls
postscreen_watchdog_timeout = 10s
postscreen_whitelist_interfaces = static:all
prepend_delivered_header = command, file, forward
process_id_directory = pid
propagate_unmatched_extensions = canonical, virtual
proxy_interfaces =
proxy_read_maps = $local_recipient_maps $mydestination $virtual_alias_maps $virtual_alias_domains $virtual_mailbox_maps $virtual_mailbox_domains $relay_recipient_maps $relay_domains $canonical_maps $sender_canonical_maps $recipient_canonical_maps $relocated_maps $transport_maps $mynetworks $smtpd_sender_login_maps $sender_bcc_maps $recipient_bcc_maps $smtp_generic_maps $lmtp_generic_maps $alias_maps $smtpd_client_restrictions $smtpd_helo_restrictions $smtpd_sender_restrictions $smtpd_relay_restrictions $smtpd_recipient_restrictions $address_verify_sender_dependent_default_transport_maps $address_verify_sender_dependent_relayhost_maps $address_verify_transport_maps $fallback_transport_maps $lmtp_discard_lhlo_keyword_address_maps $lmtp_pix_workaround_maps $lmtp_sasl_password_maps $lmtp_tls_policy_maps $mailbox_command_maps $mailbox_transport_maps $postscreen_discard_ehlo_keyword_address_maps $rbl_reply_maps $sender_dependent_default_transport_maps $sender_dependent_relayhost_maps $smtp_discard_ehlo_keyword_address_maps $smtp_pix_workaround_maps $smtp_sasl_password_maps $smtp_tls_policy_maps $smtpd_discard_ehlo_keyword_address_maps $smtpd_milter_maps $virtual_gid_maps $virtual_uid_maps
proxy_write_maps = $smtp_sasl_auth_cache_name $lmtp_sasl_auth_cache_name $address_verify_map $postscreen_cache_map
proxymap_service_name = proxymap
proxywrite_service_name = proxywrite
qmgr_clog_warn_time = 300s
qmgr_daemon_timeout = 1000s
qmgr_fudge_factor = 100
qmgr_ipc_timeout = 60s
qmgr_message_active_limit = 20000
qmgr_message_recipient_limit = 20000
qmgr_message_recipient_minimum = 10
qmqpd_authorized_clients =
qmqpd_client_port_logging = no
qmqpd_error_delay = 1s
qmqpd_timeout = 300s
queue_directory = /home/<USER>/Documents/git/swaks/testing/mta/postfix-install/queue
queue_file_attribute_count_limit = 100
queue_minfree = 0
queue_run_delay = 300s
queue_service_name = qmgr
rbl_reply_maps =
readme_directory = /home/<USER>/Documents/git/swaks/testing/mta/postfix-install/readme
receive_override_options =
recipient_bcc_maps =
recipient_canonical_classes = envelope_recipient, header_recipient
recipient_canonical_maps =
recipient_delimiter =
reject_code = 554
reject_tempfail_action = defer_if_permit
relay_clientcerts =
relay_delivery_slot_cost = $default_delivery_slot_cost
relay_delivery_slot_discount = $default_delivery_slot_discount
relay_delivery_slot_loan = $default_delivery_slot_loan
relay_destination_concurrency_failed_cohort_limit = $default_destination_concurrency_failed_cohort_limit
relay_destination_concurrency_limit = $default_destination_concurrency_limit
relay_destination_concurrency_negative_feedback = $default_destination_concurrency_negative_feedback
relay_destination_concurrency_positive_feedback = $default_destination_concurrency_positive_feedback
relay_destination_rate_delay = $default_destination_rate_delay
relay_destination_recipient_limit = $default_destination_recipient_limit
relay_domains = ${{$compatibility_level} < {2} ? {$mydestination} : {}}
relay_domains_reject_code = 554
relay_extra_recipient_limit = $default_extra_recipient_limit
relay_initial_destination_concurrency = $initial_destination_concurrency
relay_minimum_delivery_slots = $default_minimum_delivery_slots
relay_recipient_limit = $default_recipient_limit
relay_recipient_maps =
relay_recipient_refill_delay = $default_recipient_refill_delay
relay_recipient_refill_limit = $default_recipient_refill_limit
relay_transport = relay
relay_transport_rate_delay = $default_transport_rate_delay
relayhost =
relocated_maps =
remote_header_rewrite_domain =
require_home_directory = no
reset_owner_alias = no
resolve_dequoted_address = yes
resolve_null_domain = no
resolve_numeric_domain = no
retry_delivery_slot_cost = $default_delivery_slot_cost
retry_delivery_slot_discount = $default_delivery_slot_discount
retry_delivery_slot_loan = $default_delivery_slot_loan
retry_destination_concurrency_failed_cohort_limit = $default_destination_concurrency_failed_cohort_limit
retry_destination_concurrency_limit = $default_destination_concurrency_limit
retry_destination_concurrency_negative_feedback = $default_destination_concurrency_negative_feedback
retry_destination_concurrency_positive_feedback = $default_destination_concurrency_positive_feedback
retry_destination_rate_delay = $default_destination_rate_delay
retry_destination_recipient_limit = $default_destination_recipient_limit
retry_extra_recipient_limit = $default_extra_recipient_limit
retry_initial_destination_concurrency = $initial_destination_concurrency
retry_minimum_delivery_slots = $default_minimum_delivery_slots
retry_recipient_limit = $default_recipient_limit
retry_recipient_refill_delay = $default_recipient_refill_delay
retry_recipient_refill_limit = $default_recipient_refill_limit
retry_transport_rate_delay = $default_transport_rate_delay
rewrite_service_name = rewrite
sample_directory = /home/<USER>/Documents/git/swaks/testing/mta/postfix-config
send_cyrus_sasl_authzid = no
sender_bcc_maps =
sender_canonical_classes = envelope_sender, header_sender
sender_canonical_maps =
sender_dependent_default_transport_maps =
sender_dependent_relayhost_maps =
sendmail_fix_line_endings = always
sendmail_path = /home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/sendmail
service_name =
service_throttle_time = 60s
setgid_group = postdrop
shlib_directory = no
show_user_unknown_table_name = yes
showq_service_name = showq
smtp_address_preference = any
smtp_address_verify_target = rcpt
smtp_always_send_ehlo = yes
smtp_balance_inet_protocols = yes
smtp_bind_address =
smtp_bind_address6 =
smtp_body_checks =
smtp_cname_overrides_servername = no
smtp_connect_timeout = 30s
smtp_connection_cache_destinations =
smtp_connection_cache_on_demand = yes
smtp_connection_cache_time_limit = 2s
smtp_connection_reuse_count_limit = 0
smtp_connection_reuse_time_limit = 300s
smtp_data_done_timeout = 600s
smtp_data_init_timeout = 120s
smtp_data_xfer_timeout = 180s
smtp_defer_if_no_mx_address_found = no
smtp_delivery_slot_cost = $default_delivery_slot_cost
smtp_delivery_slot_discount = $default_delivery_slot_discount
smtp_delivery_slot_loan = $default_delivery_slot_loan
smtp_delivery_status_filter = $default_delivery_status_filter
smtp_destination_concurrency_failed_cohort_limit = $default_destination_concurrency_failed_cohort_limit
smtp_destination_concurrency_limit = $default_destination_concurrency_limit
smtp_destination_concurrency_negative_feedback = $default_destination_concurrency_negative_feedback
smtp_destination_concurrency_positive_feedback = $default_destination_concurrency_positive_feedback
smtp_destination_rate_delay = $default_destination_rate_delay
smtp_destination_recipient_limit = $default_destination_recipient_limit
smtp_discard_ehlo_keyword_address_maps =
smtp_discard_ehlo_keywords =
smtp_dns_reply_filter =
smtp_dns_resolver_options =
smtp_dns_support_level =
smtp_enforce_tls = no
smtp_extra_recipient_limit = $default_extra_recipient_limit
smtp_fallback_relay = $fallback_relay
smtp_generic_maps =
smtp_header_checks =
smtp_helo_name = $myhostname
smtp_helo_timeout = 300s
smtp_host_lookup = dns
smtp_initial_destination_concurrency = $initial_destination_concurrency
smtp_line_length_limit = 998
smtp_mail_timeout = 300s
smtp_mime_header_checks =
smtp_minimum_delivery_slots = $default_minimum_delivery_slots
smtp_mx_address_limit = 5
smtp_mx_session_limit = 2
smtp_nested_header_checks =
smtp_never_send_ehlo = no
smtp_per_record_deadline = no
smtp_pix_workaround_delay_time = 10s
smtp_pix_workaround_maps =
smtp_pix_workaround_threshold_time = 500s
smtp_pix_workarounds = disable_esmtp,delay_dotcrlf
smtp_quit_timeout = 300s
smtp_quote_rfc821_envelope = yes
smtp_randomize_addresses = yes
smtp_rcpt_timeout = 300s
smtp_recipient_limit = $default_recipient_limit
smtp_recipient_refill_delay = $default_recipient_refill_delay
smtp_recipient_refill_limit = $default_recipient_refill_limit
smtp_reply_filter =
smtp_rset_timeout = 20s
smtp_sasl_auth_cache_name =
smtp_sasl_auth_cache_time = 90d
smtp_sasl_auth_enable = no
smtp_sasl_auth_soft_bounce = yes
smtp_sasl_mechanism_filter =
smtp_sasl_password_maps =
smtp_sasl_path =
smtp_sasl_security_options = noplaintext, noanonymous
smtp_sasl_tls_security_options = $smtp_sasl_security_options
smtp_sasl_tls_verified_security_options = $smtp_sasl_tls_security_options
smtp_sasl_type = cyrus
smtp_send_dummy_mail_auth = no
smtp_send_xforward_command = no
smtp_sender_dependent_authentication = no
smtp_skip_5xx_greeting = yes
smtp_skip_quit_response = yes
smtp_starttls_timeout = 300s
smtp_tcp_port = smtp
smtp_tls_CAfile =
smtp_tls_CApath =
smtp_tls_block_early_mail_reply = no
smtp_tls_cert_file =
smtp_tls_chain_files =
smtp_tls_ciphers = medium
smtp_tls_connection_reuse = no
smtp_tls_dane_insecure_mx_policy = dane
smtp_tls_dcert_file =
smtp_tls_dkey_file = $smtp_tls_dcert_file
smtp_tls_eccert_file =
smtp_tls_eckey_file = $smtp_tls_eccert_file
smtp_tls_enforce_peername = yes
smtp_tls_exclude_ciphers =
smtp_tls_fingerprint_cert_match =
smtp_tls_fingerprint_digest = md5
smtp_tls_force_insecure_host_tlsa_lookup = no
smtp_tls_key_file = $smtp_tls_cert_file
smtp_tls_loglevel = 0
smtp_tls_mandatory_ciphers = medium
smtp_tls_mandatory_exclude_ciphers =
smtp_tls_mandatory_protocols = !SSLv2, !SSLv3
smtp_tls_note_starttls_offer = no
smtp_tls_per_site =
smtp_tls_policy_maps =
smtp_tls_protocols = !SSLv2, !SSLv3
smtp_tls_scert_verifydepth = 9
smtp_tls_secure_cert_match = nexthop, dot-nexthop
smtp_tls_security_level =
smtp_tls_servername =
smtp_tls_session_cache_database =
smtp_tls_session_cache_timeout = 3600s
smtp_tls_trust_anchor_file =
smtp_tls_verify_cert_match = hostname
smtp_tls_wrappermode = no
smtp_transport_rate_delay = $default_transport_rate_delay
smtp_use_tls = no
smtp_xforward_timeout = 300s
smtpd_authorized_verp_clients = $authorized_verp_clients
smtpd_authorized_xclient_hosts =
smtpd_authorized_xforward_hosts =
smtpd_banner = $myhostname ESMTP $mail_name
smtpd_client_auth_rate_limit = 0
smtpd_client_connection_count_limit = 50
smtpd_client_connection_rate_limit = 0
smtpd_client_event_limit_exceptions = ${smtpd_client_connection_limit_exceptions:$mynetworks}
smtpd_client_message_rate_limit = 0
smtpd_client_new_tls_session_rate_limit = 0
smtpd_client_port_logging = no
smtpd_client_recipient_rate_limit = 0
smtpd_client_restrictions =
smtpd_command_filter =
smtpd_data_restrictions =
smtpd_delay_open_until_valid_rcpt = yes
smtpd_delay_reject = yes
smtpd_discard_ehlo_keyword_address_maps =
smtpd_discard_ehlo_keywords =
smtpd_dns_reply_filter =
smtpd_end_of_data_restrictions =
smtpd_enforce_tls = no
smtpd_error_sleep_time = 1s
smtpd_etrn_restrictions =
smtpd_expansion_filter = \t\40!"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~
smtpd_forbidden_commands = CONNECT GET POST
smtpd_hard_error_limit = ${stress?{1}:{20}}
smtpd_helo_required = no
smtpd_helo_restrictions =
smtpd_history_flush_threshold = 100
smtpd_junk_command_limit = ${stress?{1}:{100}}
smtpd_log_access_permit_actions =
smtpd_milter_maps =
smtpd_milters =
smtpd_noop_commands =
smtpd_null_access_lookup_key = <>
smtpd_peername_lookup = yes
smtpd_per_record_deadline = ${stress?{yes}:{no}}
smtpd_policy_service_default_action = 451 4.3.5 Server configuration problem
smtpd_policy_service_max_idle = 300s
smtpd_policy_service_max_ttl = 1000s
smtpd_policy_service_policy_context =
smtpd_policy_service_request_limit = 0
smtpd_policy_service_retry_delay = 1s
smtpd_policy_service_timeout = 100s
smtpd_policy_service_try_limit = 2
smtpd_proxy_ehlo = $myhostname
smtpd_proxy_filter =
smtpd_proxy_options =
smtpd_proxy_timeout = 100s
smtpd_recipient_limit = 1000
smtpd_recipient_overshoot_limit = 1000
smtpd_recipient_restrictions =
smtpd_reject_footer =
smtpd_reject_footer_maps =
smtpd_reject_unlisted_recipient = yes
smtpd_reject_unlisted_sender = no
smtpd_relay_restrictions = ${{$compatibility_level} < {1} ? {} : {permit_mynetworks, permit_sasl_authenticated, defer_unauth_destination}}
smtpd_restriction_classes =
smtpd_sasl_auth_enable = no
smtpd_sasl_authenticated_header = no
smtpd_sasl_exceptions_networks =
smtpd_sasl_local_domain =
smtpd_sasl_path = smtpd
smtpd_sasl_response_limit = 12288
smtpd_sasl_security_options = noanonymous
smtpd_sasl_service = smtp
smtpd_sasl_tls_security_options = $smtpd_sasl_security_options
smtpd_sasl_type = cyrus
smtpd_sender_login_maps =
smtpd_sender_restrictions =
smtpd_service_name = smtpd
smtpd_soft_error_limit = 10
smtpd_starttls_timeout = ${stress?{10}:{300}}s
smtpd_timeout = ${stress?{10}:{300}}s
smtpd_tls_CAfile =
smtpd_tls_CApath =
smtpd_tls_always_issue_session_ids = yes
smtpd_tls_ask_ccert = no
smtpd_tls_auth_only = no
smtpd_tls_ccert_verifydepth = 9
smtpd_tls_cert_file =
smtpd_tls_chain_files =
smtpd_tls_ciphers = medium
smtpd_tls_dcert_file =
smtpd_tls_dh1024_param_file =
smtpd_tls_dh512_param_file =
smtpd_tls_dkey_file = $smtpd_tls_dcert_file
smtpd_tls_eccert_file =
smtpd_tls_eckey_file = $smtpd_tls_eccert_file
smtpd_tls_eecdh_grade = auto
smtpd_tls_exclude_ciphers =
smtpd_tls_fingerprint_digest = md5
smtpd_tls_key_file = $smtpd_tls_cert_file
smtpd_tls_loglevel = 0
smtpd_tls_mandatory_ciphers = medium
smtpd_tls_mandatory_exclude_ciphers =
smtpd_tls_mandatory_protocols = !SSLv2, !SSLv3
smtpd_tls_protocols = !SSLv2, !SSLv3
smtpd_tls_received_header = no
smtpd_tls_req_ccert = no
smtpd_tls_security_level =
smtpd_tls_session_cache_database =
smtpd_tls_session_cache_timeout = 3600s
smtpd_tls_wrappermode = no
smtpd_upstream_proxy_protocol =
smtpd_upstream_proxy_timeout = 5s
smtpd_use_tls = no
smtputf8_autodetect_classes = sendmail, verify
smtputf8_enable = no
soft_bounce = no
stale_lock_time = 500s
stress =
strict_7bit_headers = no
strict_8bitmime = no
strict_8bitmime_body = no
strict_mailbox_ownership = yes
strict_mime_encoding_domain = no
strict_rfc821_envelopes = no
strict_smtputf8 = no
sun_mailtool_compatibility = no
swap_bangpath = yes
syslog_facility = mail
syslog_name = ${multi_instance_name?{$multi_instance_name}:{postfix}}
tcp_windowsize = 0
tls_append_default_CA = no
tls_daemon_random_bytes = 32
tls_dane_digests = sha512 sha256
tls_disable_workarounds =
tls_eecdh_auto_curves = X25519 X448 prime256v1 secp521r1 secp384r1
tls_eecdh_strong_curve = prime256v1
tls_eecdh_ultra_curve = secp384r1
tls_export_cipherlist = aNULL:-aNULL:HIGH:MEDIUM:LOW:EXPORT:+RC4:@STRENGTH
tls_fast_shutdown_enable = yes
tls_high_cipherlist = aNULL:-aNULL:HIGH:@STRENGTH
tls_legacy_public_key_fingerprints = no
tls_low_cipherlist = aNULL:-aNULL:HIGH:MEDIUM:LOW:+RC4:@STRENGTH
tls_medium_cipherlist = aNULL:-aNULL:HIGH:MEDIUM:+RC4:@STRENGTH
tls_null_cipherlist = eNULL:!aNULL
tls_preempt_cipherlist = no
tls_random_bytes = 32
tls_random_exchange_name = ${data_directory}/prng_exch
tls_random_prng_update_period = 3600s
tls_random_reseed_period = 3600s
tls_random_source = dev:/dev/urandom
tls_server_sni_maps =
tls_session_ticket_cipher = aes-256-cbc
tls_ssl_options =
tls_wildcard_matches_multiple_labels = yes
tlsmgr_service_name = tlsmgr
tlsproxy_client_CAfile = $smtp_tls_CAfile
tlsproxy_client_CApath = $smtp_tls_CApath
tlsproxy_client_cert_file = $smtp_tls_cert_file
tlsproxy_client_chain_files = $smtp_tls_chain_files
tlsproxy_client_dcert_file = $smtp_tls_dcert_file
tlsproxy_client_dkey_file = $smtp_tls_dkey_file
tlsproxy_client_eccert_file = $smtp_tls_eccert_file
tlsproxy_client_eckey_file = $smtp_tls_eckey_file
tlsproxy_client_enforce_tls = $smtp_enforce_tls
tlsproxy_client_fingerprint_digest = $smtp_tls_fingerprint_digest
tlsproxy_client_key_file = $smtp_tls_key_file
tlsproxy_client_level = $smtp_tls_security_level
tlsproxy_client_loglevel = $smtp_tls_loglevel
tlsproxy_client_loglevel_parameter = smtp_tls_loglevel
tlsproxy_client_per_site = $smtp_tls_per_site
tlsproxy_client_policy = $smtp_tls_policy_maps
tlsproxy_client_scert_verifydepth = $smtp_tls_scert_verifydepth
tlsproxy_client_use_tls = $smtp_use_tls
tlsproxy_enforce_tls = $smtpd_enforce_tls
tlsproxy_service_name = tlsproxy
tlsproxy_tls_CAfile = $smtpd_tls_CAfile
tlsproxy_tls_CApath = $smtpd_tls_CApath
tlsproxy_tls_always_issue_session_ids = $smtpd_tls_always_issue_session_ids
tlsproxy_tls_ask_ccert = $smtpd_tls_ask_ccert
tlsproxy_tls_ccert_verifydepth = $smtpd_tls_ccert_verifydepth
tlsproxy_tls_cert_file = $smtpd_tls_cert_file
tlsproxy_tls_chain_files = $smtpd_tls_chain_files
tlsproxy_tls_ciphers = $smtpd_tls_ciphers
tlsproxy_tls_dcert_file = $smtpd_tls_dcert_file
tlsproxy_tls_dh1024_param_file = $smtpd_tls_dh1024_param_file
tlsproxy_tls_dh512_param_file = $smtpd_tls_dh512_param_file
tlsproxy_tls_dkey_file = $smtpd_tls_dkey_file
tlsproxy_tls_eccert_file = $smtpd_tls_eccert_file
tlsproxy_tls_eckey_file = $smtpd_tls_eckey_file
tlsproxy_tls_eecdh_grade = $smtpd_tls_eecdh_grade
tlsproxy_tls_exclude_ciphers = $smtpd_tls_exclude_ciphers
tlsproxy_tls_fingerprint_digest = $smtpd_tls_fingerprint_digest
tlsproxy_tls_key_file = $smtpd_tls_key_file
tlsproxy_tls_loglevel = $smtpd_tls_loglevel
tlsproxy_tls_mandatory_ciphers = $smtpd_tls_mandatory_ciphers
tlsproxy_tls_mandatory_exclude_ciphers = $smtpd_tls_mandatory_exclude_ciphers
tlsproxy_tls_mandatory_protocols = $smtpd_tls_mandatory_protocols
tlsproxy_tls_protocols = $smtpd_tls_protocols
tlsproxy_tls_req_ccert = $smtpd_tls_req_ccert
tlsproxy_tls_security_level = $smtpd_tls_security_level
tlsproxy_use_tls = $smtpd_use_tls
tlsproxy_watchdog_timeout = 10s
trace_service_name = trace
transport_maps =
transport_retry_time = 60s
trigger_timeout = 10s
undisclosed_recipients_header =
unknown_address_reject_code = 450
unknown_address_tempfail_action = $reject_tempfail_action
unknown_client_reject_code = 450
unknown_helo_hostname_tempfail_action = $reject_tempfail_action
unknown_hostname_reject_code = 450
unknown_local_recipient_reject_code = 550
unknown_relay_recipient_reject_code = 550
unknown_virtual_alias_reject_code = 550
unknown_virtual_mailbox_reject_code = 550
unverified_recipient_defer_code = 450
unverified_recipient_reject_code = 450
unverified_recipient_reject_reason =
unverified_recipient_tempfail_action = $reject_tempfail_action
unverified_sender_defer_code = 450
unverified_sender_reject_code = 450
unverified_sender_reject_reason =
unverified_sender_tempfail_action = $reject_tempfail_action
verp_delimiter_filter = -=+
virtual_alias_address_length_limit = 1000
virtual_alias_domains = $virtual_alias_maps
virtual_alias_expansion_limit = 1000
virtual_alias_maps = $virtual_maps
virtual_alias_recursion_limit = 1000
virtual_delivery_slot_cost = $default_delivery_slot_cost
virtual_delivery_slot_discount = $default_delivery_slot_discount
virtual_delivery_slot_loan = $default_delivery_slot_loan
virtual_delivery_status_filter = $default_delivery_status_filter
virtual_destination_concurrency_failed_cohort_limit = $default_destination_concurrency_failed_cohort_limit
virtual_destination_concurrency_limit = $default_destination_concurrency_limit
virtual_destination_concurrency_negative_feedback = $default_destination_concurrency_negative_feedback
virtual_destination_concurrency_positive_feedback = $default_destination_concurrency_positive_feedback
virtual_destination_rate_delay = $default_destination_rate_delay
virtual_destination_recipient_limit = $default_destination_recipient_limit
virtual_extra_recipient_limit = $default_extra_recipient_limit
virtual_gid_maps =
virtual_initial_destination_concurrency = $initial_destination_concurrency
virtual_mailbox_base =
virtual_mailbox_domains = $virtual_mailbox_maps
virtual_mailbox_limit = 51200000
virtual_mailbox_lock = fcntl, dotlock
virtual_mailbox_maps =
virtual_minimum_delivery_slots = $default_minimum_delivery_slots
virtual_minimum_uid = 100
virtual_recipient_limit = $default_recipient_limit
virtual_recipient_refill_delay = $default_recipient_refill_delay
virtual_recipient_refill_limit = $default_recipient_refill_limit
virtual_transport = virtual
virtual_transport_rate_delay = $default_transport_rate_delay
virtual_uid_maps =
