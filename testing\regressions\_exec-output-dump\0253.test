auto: REMOVE_FILE,CREATE_FILE,<PERSON>UN<PERSON>,COMPARE_FILE %TESTID%.stdout %TESTID%.stderr %TESTID%.exits

test action: CMD_CAPTURE %SWAKS% --dump proxy --to <EMAIL> --server ser.ver --helo host1.nodns.test.swaks.net --from <EMAIL> --proxy-version 2 --proxy-family AF_INET --proxy-source ******* --proxy-source-port 99 --proxy-dest ******* --proxy-dest-port 88 --proxy-protocol DGRAM --proxy-command LOCAL
