auto: REMOVE_FILE,CREATE_FILE,MUN<PERSON>,COMPARE_FILE %TESTID%.stdout %TESTID%.stderr %TESTID%.exits

#debug: SET_ENV

# create a file that will we consumed by swaks and munge the requirements
pre action: MERGE ./%OUTDIR%/munge-requirements.pl mode:0600 string:'$G::dependencies{auth}{opt} = ["Unavailable::Module"]; return 1;\n'

# set SWAKS_ALTER_REQUIREMENTS to the file we just created so it will be executed
pre action: SET_ENV PERL5LIB .
pre action: SET_ENV SWAKS_ALTER_REQUIREMENTS %OUTDIR%/munge-requirements.pl


test action: CMD_CAPTURE %SWAKS% --to <EMAIL> --server ser.ver --helo host1.nodns.test.swaks.net --from <EMAIL> \
  --support
