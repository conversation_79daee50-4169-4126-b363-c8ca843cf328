<!DOCTYPE html>
<html lang="en">
<head>
    <title>Swaks - Swiss Army Knife for SMTP</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/skeleton/2.0.4/skeleton.min.css" /> <!-- https://cdnjs.com/libraries/skeleton -->
    <style type="text/css">
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif }
        #subheader { font-size: 120% }
        #rss { font-size: 70% }
        a:link { color: blue }
        a:visited { color: blue }
        a:active { color: navy }
        .container { max-width: 600px }
        .header { margin-top: 3% }
        .footer { margin-bottom: 3% }
    </style>
</head>
<body>

<div class="section header">
    <div class="container">
        <div class="row">
            <a name="swaks"><h1><b>Swaks</b> - Swiss Army Knife for SMTP</h1></a>
            <!--
            <span id="subheader">A scriptable, transaction-oriented SMTP test tool</span>
            -->
        </div>
        <hr/>
    </div>
</div>

<div class="container">
    <div class="row">
        <h4>About</h4>
        <p>
            Swaks is a featureful, flexible, scriptable, transaction-oriented SMTP test tool written
            and maintained by <a href="https://www.jetmore.org/john/">John Jetmore</a>.
            It is free to use and <a href="latest/LICENSE.txt">licensed</a> under the GNU GPLv2.
        </p>
        <p><b>Features include:</b></p>
        <ul>
            <li>SMTP extensions including TLS, authentication, pipelining, PROXY, PRDR, and XCLIENT</li>
            <li>Protocols including SMTP, ESMTP, and LMTP</li>
            <li>Transports including UNIX-domain sockets, internet-domain sockets (IPv4 and IPv6), and pipes to spawned processes</li>
            <li>Completely scriptable configuration, with option specification via environment variables, configuration files, and command line</li>
        </ul>
    </div>

    <hr/>

    <div class="row">
        <h4>Download</h4>
        <p>
            The latest version of Swaks is <b>20240103.0</b> (<a href="https://www.jetmore.org/john/blog/2024/01/swaks-release-20240103-0-available/">announcement</a>), which can be downloaded as a <a href="files/swaks-20240103.0.tar.gz">package</a> or a <a href="files/swaks-20240103.0/swaks">standalone script</a>.
        </p>
        <p>
            See the <a href="installation.html">installation page</a> for details on installing in multiple environments.
        <p/>
        <p>
            The <a href="versions.html">versions page</a> lists every released version of Swaks, complete with changelogs and download links.
        </p>
    </div>

    <div class="row">
        <h4>Documentation</h4>
        <p>
            The reference documentation from the latest release,
            which includes quick-start examples, is available as <a href="latest/doc/ref.txt">plain text</a> and <a href="https://github.com/jetmore/swaks/blob/v20240103.0/doc/base.pod">rendered</a>.
            The documentation from each release is available from the <a href="versions.html">versions page</a>.
            There is also an <a href="faq.html">Occasionally Asked Questions</a> document.
        </p>
    </div>

    <div class="row">
        <h4>Source</h4>
        <p>The Swaks source code is available at <a href="https://github.com/jetmore/swaks">https://github.com/jetmore/swaks</a>.</p>
    </div>

    <div class="row">
        <h4>Communications</h4>
        <ul>
            <li><a href="mailto:<EMAIL>">Send a mail</a> to receive email when new versions are released</li>
            <li><a href="https://twitter.com/SwaksSMTP">Follow @SwaksSMTP</a> on twitter</li>
            <li><a href="mailto:<EMAIL>">Contact the author</a> - suggestion, tips, patches, feedback, critiques always welcome</li>
            <li><a href="https://www.jetmore.org/john/blog/c/swaks/">Blog</a> - Swaks-specific blog category (<a href="https://www.jetmore.org/john/blog/c/swaks/feed/">rss</a> also available)</li>
            <li><a href="https://github.com/jetmore/swaks/issues">Issues</a> - Open an issue for feature requests and bugs</li>
        </ul>
    </div>
</div> <!-- end of content container -->

<div class="section footer">
    <div class="container">
        <div class="row">
            <hr/>
            <p><a href="/john/">www.jetmore.org/john</a> / <a href="/john/code/">code</a> / <b>swaks</b></p>
        </div>
    </div>
</div>

</body>
</html>
