#
# Do not edit this file.
#
# This file controls the postfix-install script for installation of
# Postfix programs, configuration files and documentation, as well
# as the post-install script for setting permissions and for updating
# Postfix configuration files. See the respective manual pages within
# the script files.
#
# Do not list $command_directory or $shlib_directory in this file,
# or it will be blown away by a future Postfix uninstallation
# procedure. You would not want to lose all files in /usr/sbin or
# /usr/local/lib.
#
# Each record in this file describes one file or directory.
# Fields are separated by ":". Specify a null field as "-".
# Missing fields or separators at the end are OK.
#
# File format:
#	name:type:owner:group:permission:flags
#	No group means don't change group ownership.
#
# File types:
#	d=directory
#	f=regular file
#	h=hard link (*)
#	l=symbolic link (*)
#
# (*) With hard links and symbolic links, the owner field becomes the
#     source pathname, while the group and permissions are ignored.
#
# File flags:
#	No flag means the flag is not active.
#	p=preserve existing file, do not replace (postfix-install).
#	u=update owner/group/mode (post-install upgrade-permissions).
#	c=create missing directory (post-install create-missing).
#	r=apply owner/group recursively (post-install set/upgrade-permissions).
#	o=obsolete, no longer part of Postfix
#	1=optional for non-default instance (config_dir != built-in default).
#
# Note: the "u" flag is for upgrading the permissions of existing files
# or directories after changes in Postfix architecture. For robustness
# it is a good idea to "u" all the files that have special ownership or
# permissions, so that running "make install" fixes any glitches.
#
# Note: order matters. Update shared libraries and database plugins
# before daemon/command-line programs.
$config_directory:d:root:-:755:u
$data_directory:d:$mail_owner:-:700:uc
$daemon_directory:d:root:-:755:u
$queue_directory:d:root:-:755:uc
$sample_directory:d:root:-:755:o
$readme_directory:d:root:-:755
$html_directory:d:root:-:755
$queue_directory/active:d:$mail_owner:-:700:ucr
$queue_directory/bounce:d:$mail_owner:-:700:ucr
$queue_directory/corrupt:d:$mail_owner:-:700:ucr
$queue_directory/defer:d:$mail_owner:-:700:ucr
$queue_directory/deferred:d:$mail_owner:-:700:ucr
$queue_directory/flush:d:$mail_owner:-:700:ucr
$queue_directory/hold:d:$mail_owner:-:700:ucr
$queue_directory/incoming:d:$mail_owner:-:700:ucr
$queue_directory/private:d:$mail_owner:-:700:uc
$queue_directory/maildrop:d:$mail_owner:$setgid_group:730:uc
$queue_directory/public:d:$mail_owner:$setgid_group:710:uc
$queue_directory/pid:d:root:-:755:uc
$queue_directory/saved:d:$mail_owner:-:700:ucr
$queue_directory/trace:d:$mail_owner:-:700:ucr
# Update shared libraries and plugins before daemon or command-line programs.
$meta_directory/main.cf.proto:f:root:-:644
$meta_directory/makedefs.out:f:root:-:644
$meta_directory/master.cf.proto:f:root:-:644
$meta_directory/postfix-files.d:d:root:-:755
$meta_directory/postfix-files:f:root:-:644
$daemon_directory/anvil:f:root:-:755
$daemon_directory/bounce:f:root:-:755
$daemon_directory/cleanup:f:root:-:755
$daemon_directory/discard:f:root:-:755
$daemon_directory/dnsblog:f:root:-:755
$daemon_directory/error:f:root:-:755
$daemon_directory/flush:f:root:-:755
$daemon_directory/local:f:root:-:755
$daemon_directory/main.cf:f:root:-:644:o
$daemon_directory/master.cf:f:root:-:644:o
$daemon_directory/master:f:root:-:755
$daemon_directory/oqmgr:f:root:-:755
$daemon_directory/pickup:f:root:-:755
$daemon_directory/pipe:f:root:-:755
$daemon_directory/post-install:f:root:-:755
# In case meta_directory == daemon_directory.
#$daemon_directory/postfix-files:f:root:-:644:o
#$daemon_directory/postfix-files.d:d:root:-:755:o
$daemon_directory/postfix-script:f:root:-:755
$daemon_directory/postfix-tls-script:f:root:-:755
$daemon_directory/postfix-wrapper:f:root:-:755
$daemon_directory/postmulti-script:f:root:-:755
$daemon_directory/postlogd:f:root:-:755
$daemon_directory/postscreen:f:root:-:755
$daemon_directory/proxymap:f:root:-:755
$daemon_directory/qmgr:f:root:-:755
$daemon_directory/qmqpd:f:root:-:755
$daemon_directory/scache:f:root:-:755
$daemon_directory/showq:f:root:-:755
$daemon_directory/smtp:f:root:-:755
$daemon_directory/smtpd:f:root:-:755
$daemon_directory/spawn:f:root:-:755
$daemon_directory/tlsproxy:f:root:-:755
$daemon_directory/tlsmgr:f:root:-:755
$daemon_directory/trivial-rewrite:f:root:-:755
$daemon_directory/verify:f:root:-:755
$daemon_directory/virtual:f:root:-:755
$daemon_directory/nqmgr:h:$daemon_directory/qmgr
$daemon_directory/lmtp:h:$daemon_directory/smtp
$command_directory/postalias:f:root:-:755
$command_directory/postcat:f:root:-:755
$command_directory/postconf:f:root:-:755
$command_directory/postfix:f:root:-:755
$command_directory/postkick:f:root:-:755
$command_directory/postlock:f:root:-:755
$command_directory/postlog:f:root:-:755
$command_directory/postmap:f:root:-:755
$command_directory/postmulti:f:root:-:755
$command_directory/postsuper:f:root:-:755
$command_directory/postdrop:f:root:$setgid_group:2755:u
$command_directory/postqueue:f:root:$setgid_group:2755:u
$sendmail_path:f:root:-:755
$newaliases_path:l:$sendmail_path
$mailq_path:l:$sendmail_path
$config_directory/LICENSE:f:root:-:644:1
$config_directory/TLS_LICENSE:f:root:-:644:1
$config_directory/access:f:root:-:644:p1
$config_directory/aliases:f:root:-:644:p1
$config_directory/bounce.cf.default:f:root:-:644:1
$config_directory/canonical:f:root:-:644:p1
$config_directory/cidr_table:f:root:-:644:o
$config_directory/generic:f:root:-:644:p1
$config_directory/generics:f:root:-:644:o
$config_directory/header_checks:f:root:-:644:p1
$config_directory/install.cf:f:root:-:644:o
$config_directory/main.cf.default:f:root:-:644:1
$config_directory/main.cf:f:root:-:644:p
$config_directory/master.cf:f:root:-:644:p
$config_directory/pcre_table:f:root:-:644:o
$config_directory/regexp_table:f:root:-:644:o
$config_directory/relocated:f:root:-:644:p1
$config_directory/tcp_table:f:root:-:644:o
$config_directory/transport:f:root:-:644:p1
$config_directory/virtual:f:root:-:644:p1
$config_directory/postfix-script:f:root:-:755:o
$config_directory/postfix-script-sgid:f:root:-:755:o
$config_directory/postfix-script-nosgid:f:root:-:755:o
$config_directory/post-install:f:root:-:755:o
$manpage_directory/man1/mailq.1:f:root:-:644
$manpage_directory/man1/newaliases.1:f:root:-:644
$manpage_directory/man1/postalias.1:f:root:-:644
$manpage_directory/man1/postcat.1:f:root:-:644
$manpage_directory/man1/postconf.1:f:root:-:644
$manpage_directory/man1/postdrop.1:f:root:-:644
$manpage_directory/man1/postfix.1:f:root:-:644
$manpage_directory/man1/postfix-tls.1:f:root:-:644
$manpage_directory/man1/postkick.1:f:root:-:644
$manpage_directory/man1/postlock.1:f:root:-:644
$manpage_directory/man1/postlog.1:f:root:-:644
$manpage_directory/man1/postmap.1:f:root:-:644
$manpage_directory/man1/postmulti.1:f:root:-:644
$manpage_directory/man1/postqueue.1:f:root:-:644
$manpage_directory/man1/postsuper.1:f:root:-:644
$manpage_directory/man1/sendmail.1:f:root:-:644
$manpage_directory/man5/access.5:f:root:-:644
$manpage_directory/man5/aliases.5:f:root:-:644
$manpage_directory/man5/body_checks.5:f:root:-:644
$manpage_directory/man5/bounce.5:f:root:-:644
$manpage_directory/man5/canonical.5:f:root:-:644
$manpage_directory/man5/cidr_table.5:f:root:-:644
$manpage_directory/man5/generics.5:f:root:-:644:o
$manpage_directory/man5/generic.5:f:root:-:644
$manpage_directory/man5/header_checks.5:f:root:-:644
$manpage_directory/man5/ldap_table.5:f:root:-:644
$manpage_directory/man5/lmdb_table.5:f:root:-:644
$manpage_directory/man5/master.5:f:root:-:644
$manpage_directory/man5/memcache_table.5:f:root:-:644
$manpage_directory/man5/mysql_table.5:f:root:-:644
$manpage_directory/man5/socketmap_table.5:f:root:-:644
$manpage_directory/man5/sqlite_table.5:f:root:-:644
$manpage_directory/man5/nisplus_table.5:f:root:-:644
$manpage_directory/man5/pcre_table.5:f:root:-:644
$manpage_directory/man5/pgsql_table.5:f:root:-:644
$manpage_directory/man5/postconf.5:f:root:-:644
$manpage_directory/man5/postfix-wrapper.5:f:root:-:644
$manpage_directory/man5/regexp_table.5:f:root:-:644
$manpage_directory/man5/relocated.5:f:root:-:644
$manpage_directory/man5/tcp_table.5:f:root:-:644
$manpage_directory/man5/transport.5:f:root:-:644
$manpage_directory/man5/virtual.5:f:root:-:644
$manpage_directory/man8/bounce.8:f:root:-:644
$manpage_directory/man8/cleanup.8:f:root:-:644
$manpage_directory/man8/anvil.8:f:root:-:644
$manpage_directory/man8/defer.8:f:root:-:644
$manpage_directory/man8/discard.8:f:root:-:644
$manpage_directory/man8/dnsblog.8:f:root:-:644
$manpage_directory/man8/error.8:f:root:-:644
$manpage_directory/man8/flush.8:f:root:-:644
$manpage_directory/man8/lmtp.8:f:root:-:644
$manpage_directory/man8/local.8:f:root:-:644
$manpage_directory/man8/master.8:f:root:-:644
$manpage_directory/man8/nqmgr.8:f:root:-:644:o
$manpage_directory/man8/oqmgr.8:f:root:-:644:
$manpage_directory/man8/pickup.8:f:root:-:644
$manpage_directory/man8/pipe.8:f:root:-:644
$manpage_directory/man8/postlogd.8:f:root:-:644
$manpage_directory/man8/postscreen.8:f:root:-:644
$manpage_directory/man8/proxymap.8:f:root:-:644
$manpage_directory/man8/qmgr.8:f:root:-:644
$manpage_directory/man8/qmqpd.8:f:root:-:644
$manpage_directory/man8/scache.8:f:root:-:644
$manpage_directory/man8/showq.8:f:root:-:644
$manpage_directory/man8/smtp.8:f:root:-:644
$manpage_directory/man8/smtpd.8:f:root:-:644
$manpage_directory/man8/spawn.8:f:root:-:644
$manpage_directory/man8/tlsproxy.8:f:root:-:644
$manpage_directory/man8/tlsmgr.8:f:root:-:644
$manpage_directory/man8/trace.8:f:root:-:644
$manpage_directory/man8/trivial-rewrite.8:f:root:-:644
$manpage_directory/man8/verify.8:f:root:-:644
$manpage_directory/man8/virtual.8:f:root:-:644
$sample_directory/sample-aliases.cf:f:root:-:644:o
$sample_directory/sample-auth.cf:f:root:-:644:o
$sample_directory/sample-canonical.cf:f:root:-:644:o
$sample_directory/sample-compatibility.cf:f:root:-:644:o
$sample_directory/sample-debug.cf:f:root:-:644:o
$sample_directory/sample-filter.cf:f:root:-:644:o
$sample_directory/sample-flush.cf:f:root:-:644:o
$sample_directory/sample-ipv6.cf:f:root:-:644:o
$sample_directory/sample-ldap.cf:f:root:-:644:o
$sample_directory/sample-lmtp.cf:f:root:-:644:o
$sample_directory/sample-local.cf:f:root:-:644:o
$sample_directory/sample-mime.cf:f:root:-:644:o
$sample_directory/sample-misc.cf:f:root:-:644:o
$sample_directory/sample-pcre-access.cf:f:root:-:644:o
$sample_directory/sample-pcre-body.cf:f:root:-:644:o
$sample_directory/sample-pcre-header.cf:f:root:-:644:o
$sample_directory/sample-pgsql-aliases.cf:f:root:-:644:o
$sample_directory/sample-qmqpd.cf:f:root:-:644:o
$sample_directory/sample-rate.cf:f:root:-:644:o
$sample_directory/sample-regexp-access.cf:f:root:-:644:o
$sample_directory/sample-regexp-body.cf:f:root:-:644:o
$sample_directory/sample-regexp-header.cf:f:root:-:644:o
$sample_directory/sample-relocated.cf:f:root:-:644:o
$sample_directory/sample-resource.cf:f:root:-:644:o
$sample_directory/sample-rewrite.cf:f:root:-:644:o
$sample_directory/sample-scheduler.cf:f:root:-:644:o
$sample_directory/sample-smtp.cf:f:root:-:644:o
$sample_directory/sample-smtpd.cf:f:root:-:644:o
$sample_directory/sample-tls.cf:f:root:-:644:o
$sample_directory/sample-transport.cf:f:root:-:644:o
$sample_directory/sample-verify.cf:f:root:-:644:o
$sample_directory/sample-virtual.cf:f:root:-:644:o
$readme_directory/AAAREADME:f:root:-:644
$readme_directory/ADDRESS_CLASS_README:f:root:-:644
$readme_directory/ADDRESS_REWRITING_README:f:root:-:644
$readme_directory/ADDRESS_VERIFICATION_README:f:root:-:644
$readme_directory/BACKSCATTER_README:f:root:-:644
$readme_directory/BASIC_CONFIGURATION_README:f:root:-:644
$readme_directory/BUILTIN_FILTER_README:f:root:-:644
$readme_directory/CDB_README:f:root:-:644
$readme_directory/COMPATIBILITY_README:f:root:-:644
$readme_directory/CONNECTION_CACHE_README:f:root:-:644
$readme_directory/CONTENT_INSPECTION_README:f:root:-:644
$readme_directory/DATABASE_README:f:root:-:644
$readme_directory/DB_README:f:root:-:644
$readme_directory/DEBUG_README:f:root:-:644
$readme_directory/DSN_README:f:root:-:644
$readme_directory/ETRN_README:f:root:-:644
$readme_directory/FILTER_README:f:root:-:644
$readme_directory/FORWARD_SECRECY_README:f:root:-:644
$readme_directory/HOSTING_README:f:root:-:644:o
$readme_directory/INSTALL:f:root:-:644
$readme_directory/IPV6_README:f:root:-:644
$readme_directory/LDAP_README:f:root:-:644
$readme_directory/LINUX_README:f:root:-:644
$readme_directory/LMDB_README:f:root:-:644
$readme_directory/LOCAL_RECIPIENT_README:f:root:-:644
$readme_directory/MACOSX_README:f:root:-:644:o
$readme_directory/MAILDROP_README:f:root:-:644
$readme_directory/MEMCACHE_README:f:root:-:644
$readme_directory/MILTER_README:f:root:-:644
$readme_directory/MULTI_INSTANCE_README:f:root:-:644
$readme_directory/MYSQL_README:f:root:-:644
$readme_directory/SQLITE_README:f:root:-:644
$readme_directory/NFS_README:f:root:-:644
$readme_directory/OVERVIEW:f:root:-:644
$readme_directory/PACKAGE_README:f:root:-:644
$readme_directory/PCRE_README:f:root:-:644
$readme_directory/PGSQL_README:f:root:-:644
$readme_directory/POSTSCREEN_README:f:root:-:644
$readme_directory/QMQP_README:f:root:-:644:o
$readme_directory/QSHAPE_README:f:root:-:644
$readme_directory/RELEASE_NOTES:f:root:-:644
$readme_directory/RESTRICTION_CLASS_README:f:root:-:644
$readme_directory/SASL_README:f:root:-:644
$readme_directory/SCHEDULER_README:f:root:-:644
$readme_directory/SMTPD_ACCESS_README:f:root:-:644
$readme_directory/SMTPD_POLICY_README:f:root:-:644
$readme_directory/SMTPD_PROXY_README:f:root:-:644
$readme_directory/SOHO_README:f:root:-:644
$readme_directory/STANDARD_CONFIGURATION_README:f:root:-:644
$readme_directory/STRESS_README:f:root:-:644
$readme_directory/TLS_LEGACY_README:f:root:-:644
$readme_directory/TLS_README:f:root:-:644
$readme_directory/TUNING_README:f:root:-:644
$readme_directory/ULTRIX_README:f:root:-:644
$readme_directory/UUCP_README:f:root:-:644
$readme_directory/VERP_README:f:root:-:644
$readme_directory/VIRTUAL_README:f:root:-:644
$readme_directory/XCLIENT_README:f:root:-:644
$readme_directory/XFORWARD_README:f:root:-:644
$html_directory/ADDRESS_CLASS_README.html:f:root:-:644
$html_directory/ADDRESS_REWRITING_README.html:f:root:-:644
$html_directory/ADDRESS_VERIFICATION_README.html:f:root:-:644
$html_directory/BACKSCATTER_README.html:f:root:-:644
$html_directory/BASIC_CONFIGURATION_README.html:f:root:-:644
$html_directory/BUILTIN_FILTER_README.html:f:root:-:644
$html_directory/CDB_README.html:f:root:-:644
$html_directory/COMPATIBILITY_README.html:f:root:-:644
$html_directory/CONNECTION_CACHE_README.html:f:root:-:644
$html_directory/CONTENT_INSPECTION_README.html:f:root:-:644
$html_directory/CYRUS_README.html:f:root:-:644:o
$html_directory/DATABASE_README.html:f:root:-:644
$html_directory/DB_README.html:f:root:-:644
$html_directory/DEBUG_README.html:f:root:-:644
$html_directory/DSN_README.html:f:root:-:644
$html_directory/ETRN_README.html:f:root:-:644
$html_directory/FILTER_README.html:f:root:-:644
$html_directory/FORWARD_SECRECY_README.html:f:root:-:644
$html_directory/INSTALL.html:f:root:-:644
$html_directory/IPV6_README.html:f:root:-:644
$html_directory/LDAP_README.html:f:root:-:644
$html_directory/LINUX_README.html:f:root:-:644
$html_directory/LMDB_README.html:f:root:-:644
$html_directory/LOCAL_RECIPIENT_README.html:f:root:-:644
$html_directory/MAILDROP_README.html:f:root:-:644
$html_directory/MILTER_README.html:f:root:-:644
$html_directory/MULTI_INSTANCE_README.html:f:root:-:644
$html_directory/MYSQL_README.html:f:root:-:644
$html_directory/SQLITE_README.html:f:root:-:644
$html_directory/NFS_README.html:f:root:-:644
$html_directory/OVERVIEW.html:f:root:-:644
$html_directory/PACKAGE_README.html:f:root:-:644
$html_directory/PCRE_README.html:f:root:-:644
$html_directory/PGSQL_README.html:f:root:-:644
$html_directory/POSTSCREEN_README.html:f:root:-:644
$html_directory/QMQP_README.html:f:root:-:644:o
$html_directory/QSHAPE_README.html:f:root:-:644
$html_directory/RESTRICTION_CLASS_README.html:f:root:-:644
$html_directory/SASL_README.html:f:root:-:644
$html_directory/SCHEDULER_README.html:f:root:-:644
$html_directory/SMTPD_ACCESS_README.html:f:root:-:644
$html_directory/SMTPD_POLICY_README.html:f:root:-:644
$html_directory/SMTPD_PROXY_README.html:f:root:-:644
$html_directory/SOHO_README.html:f:root:-:644
$html_directory/STANDARD_CONFIGURATION_README.html:f:root:-:644
$html_directory/STRESS_README.html:f:root:-:644
$html_directory/TLS_LEGACY_README.html:f:root:-:644
$html_directory/TLS_README.html:f:root:-:644
$html_directory/TUNING_README.html:f:root:-:644
$html_directory/ULTRIX_README.html:f:root:-:644:o
$html_directory/UUCP_README.html:f:root:-:644
$html_directory/VERP_README.html:f:root:-:644
$html_directory/VIRTUAL_README.html:f:root:-:644
$html_directory/XCLIENT_README.html:f:root:-:644
$html_directory/XFORWARD_README.html:f:root:-:644
$html_directory/access.5.html:f:root:-:644
$html_directory/aliases.5.html:f:root:-:644
$html_directory/anvil.8.html:f:root:-:644
$html_directory/bounce.8.html:f:root:-:644
$html_directory/canonical.5.html:f:root:-:644
$html_directory/cidr_table.5.html:f:root:-:644
$html_directory/cleanup.8.html:f:root:-:644
$html_directory/defer.8.html:h:$html_directory/bounce.8.html:-:644
$html_directory/discard.8.html:f:root:-:644
$html_directory/dnsblog.8.html:f:root:-:644
$html_directory/error.8.html:f:root:-:644
$html_directory/flush.8.html:f:root:-:644
$html_directory/generics.5.html:f:root:-:644:o
$html_directory/generic.5.html:f:root:-:644
$html_directory/header_checks.5.html:f:root:-:644
$html_directory/index.html:f:root:-:644
$html_directory/ldap_table.5.html:f:root:-:644
$html_directory/lmtp.8.html:f:root:-:644
$html_directory/local.8.html:f:root:-:644
$html_directory/mailq.1.html:f:root:-:644
$html_directory/master.5.html:f:root:-:644
$html_directory/master.8.html:f:root:-:644
$html_directory/memcache_table.5.html:f:root:-:644
$html_directory/mysql_table.5.html:f:root:-:644
$html_directory/sqlite_table.5.html:f:root:-:644
$html_directory/nisplus_table.5.html:f:root:-:644
$html_directory/newaliases.1.html:h:$html_directory/mailq.1.html:-:644
$html_directory/oqmgr.8.html:f:root:-:644
$html_directory/pcre_table.5.html:f:root:-:644
$html_directory/pgsql_table.5.html:f:root:-:644
$html_directory/pickup.8.html:f:root:-:644
$html_directory/pipe.8.html:f:root:-:644
$html_directory/postalias.1.html:f:root:-:644
$html_directory/postcat.1.html:f:root:-:644
$html_directory/postconf.1.html:f:root:-:644
$html_directory/postconf.5.html:f:root:-:644
$html_directory/postdrop.1.html:f:root:-:644
$html_directory/postfix-logo.jpg:f:root:-:644
$html_directory/postfix-manuals.html:f:root:-:644
$html_directory/postfix-wrapper.5.html:f:root:-:644
$html_directory/postfix.1.html:f:root:-:644
$html_directory/postkick.1.html:f:root:-:644
$html_directory/postlock.1.html:f:root:-:644
$html_directory/postlog.1.html:f:root:-:644
$html_directory/postmap.1.html:f:root:-:644
$html_directory/postmulti.1.html:f:root:-:644
$html_directory/postlogd.8.html:f:root:-:644
$html_directory/postqueue.1.html:f:root:-:644
$html_directory/postscreen.8.html:f:root:-:644
$html_directory/postsuper.1.html:f:root:-:644
$html_directory/qshape.1.html:f:root:-:644
$html_directory/proxymap.8.html:f:root:-:644
$html_directory/qmgr.8.html:f:root:-:644
$html_directory/qmqp-sink.1.html:f:root:-:644
$html_directory/qmqp-source.1.html:f:root:-:644
$html_directory/qmqpd.8.html:f:root:-:644
$html_directory/regexp_table.5.html:f:root:-:644
$html_directory/relocated.5.html:f:root:-:644
$html_directory/sendmail.1.html:h:$html_directory/mailq.1.html:-:644
$html_directory/showq.8.html:f:root:-:644
$html_directory/smtp-sink.1.html:f:root:-:644
$html_directory/smtp-source.1.html:f:root:-:644
$html_directory/smtp.8.html:h:$html_directory/lmtp.8.html:-:644
$html_directory/smtpd.8.html:f:root:-:644
$html_directory/spawn.8.html:f:root:-:644
$html_directory/tlsproxy.8.html:f:root:-:644
$html_directory/tcp_table.5.html:f:root:-:644
$html_directory/trace.8.html:h:$html_directory/bounce.8.html:-:644
$html_directory/transport.5.html:f:root:-:644
$html_directory/trivial-rewrite.8.html:f:root:-:644
$html_directory/verify.8.html:f:root:-:644
$html_directory/virtual.5.html:f:root:-:644
$html_directory/virtual.8.html:f:root:-:644
