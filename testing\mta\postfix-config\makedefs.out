# Do not edit -- this file documents how Postfix was built for your machine.
#----------------------------------------------------------------
# Start of summary of user-configurable 'make makefiles' options.
# CCARGS=-DUSE_TLS
# AUXLIBS=-lssl -lcrypto
# shared=
# dynamicmaps=
# pie=
# command_directory=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin
# config_directory=/home/<USER>/Documents/git/swaks/testing/mta/postfix-config
# daemon_directory=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/libexec
# data_directory=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/lib
# mail_spool_directory=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/mailspool
# mailq_path=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/mailq
# meta_directory=/home/<USER>/Documents/git/swaks/testing/mta/postfix-config
# newaliases_path=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/newaliases
# queue_directory=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/queue
# sendmail_path=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/sendmail
# shlib_directory=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/lib
# manpage_directory=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/man
# readme_directory=/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/readme
# End of summary of user-configurable 'make makefiles' options.
#--------------------------------------------------------------
# System-dependent settings and compiler/linker overrides.
SYSTYPE	= LINUX4
_AR	= ar
ARFL	= rv
_RANLIB	= ranlib
SYSLIBS	= -lssl -lcrypto -lpcre -ldb -lnsl -lresolv -ldl 
CC	= gcc -I. -I../../include -DUSE_TLS -DNO_EAI -DDEF_SMTPUTF8_ENABLE=\"no\" -DHAS_DEV_URANDOM -DHAS_PCRE -DDEF_COMMAND_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin\" -DDEF_CONFIG_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-config\" -DDEF_DAEMON_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/libexec\" -DDEF_DATA_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/lib\" -DDEF_MAIL_SPOOL_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/mailspool\" -DDEF_MAILQ_PATH=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/mailq\" -DDEF_META_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-config\" -DDEF_NEWALIAS_PATH=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/newaliases\" -DDEF_QUEUE_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/queue\" -DDEF_SENDMAIL_PATH=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/sendmail\" -DDEF_SHLIB_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/lib\" -DDEF_MANPAGE_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/man\" -DDEF_README_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/readme\" -UUSE_DYNAMIC_LIBS -DDEF_SHLIB_DIR=\"no\" -UUSE_DYNAMIC_MAPS $(WARN)
OPT	= -O
DEBUG	= -g
AWK	= awk
STRCASE = 
EXPORT	= CCARGS='-I. -I../../include -DUSE_TLS -DNO_EAI -DDEF_SMTPUTF8_ENABLE=\"no\" -DHAS_DEV_URANDOM -DHAS_PCRE -DDEF_COMMAND_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin\" -DDEF_CONFIG_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-config\" -DDEF_DAEMON_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/libexec\" -DDEF_DATA_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/lib\" -DDEF_MAIL_SPOOL_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/mailspool\" -DDEF_MAILQ_PATH=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/mailq\" -DDEF_META_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-config\" -DDEF_NEWALIAS_PATH=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/newaliases\" -DDEF_QUEUE_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/queue\" -DDEF_SENDMAIL_PATH=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/bin/sendmail\" -DDEF_SHLIB_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/lib\" -DDEF_MANPAGE_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/man\" -DDEF_README_DIR=\"/home/<USER>/Documents/git/swaks/testing/mta/postfix-install/readme\" -UUSE_DYNAMIC_LIBS -DDEF_SHLIB_DIR=\"no\" -UUSE_DYNAMIC_MAPS' OPT='-O' DEBUG='-g'
WARN	= -Wall -Wno-comment -Wformat -Wimplicit -Wmissing-prototypes \
	-Wparentheses -Wstrict-prototypes -Wswitch -Wuninitialized \
	-Wunused -Wno-missing-braces -fcommon
DEFINED_MAP_TYPES = dev_urandom pcre
MAKE_FIX = 
# Switch between Postfix static and dynamically-linked libraries.
AR	= ar
RANLIB	= ranlib
LIB_PREFIX = 
LIB_SUFFIX = .a
SHLIB_CFLAGS = 
SHLIB_DIR = no
SHLIB_LD = :
SHLIB_SYSLIBS = 
SHLIB_RPATH = 
# Switch between dynamicmaps.cf plugins and hard-linked databases.
NON_PLUGIN_MAP_OBJ = $(MAP_OBJ)
PLUGIN_MAP_OBJ = 
PLUGIN_MAP_OBJ_UPDATE = 
PLUGIN_MAP_SO_MAKE = 
PLUGIN_MAP_SO_UPDATE = 
PLUGIN_LD = 
POSTFIX_INSTALL_OPTS = 
# Application-specific rules.
