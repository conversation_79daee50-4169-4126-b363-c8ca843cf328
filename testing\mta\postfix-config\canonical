# CANONICAL(5)                                                      CANONICAL(5)
# 
# NAME
#        canonical - Postfix canonical table format
# 
# SYNOPSIS
#        postmap /etc/postfix/canonical
# 
#        postmap -q "string" /etc/postfix/canonical
# 
#        postmap -q - /etc/postfix/canonical <inputfile
# 
# DESCRIPTION
#        The  optional canonical(5) table specifies an address map-
#        ping for local and non-local  addresses.  The  mapping  is
#        used  by the cleanup(8) daemon, before mail is stored into
#        the queue.  The address mapping is recursive.
# 
#        Normally, the canonical(5) table is specified  as  a  text
#        file  that serves as input to the postmap(1) command.  The
#        result, an indexed file in dbm or db format, is  used  for
#        fast  searching  by  the  mail system. Execute the command
#        "postmap /etc/postfix/canonical"  to  rebuild  an  indexed
#        file after changing the corresponding text file.
# 
#        When  the  table  is provided via other means such as NIS,
#        LDAP or SQL, the same lookups are  done  as  for  ordinary
#        indexed files.
# 
#        Alternatively,  the  table  can  be  provided  as  a regu-
#        lar-expression map where patterns  are  given  as  regular
#        expressions,  or  lookups  can  be  directed  to TCP-based
#        server. In those cases, the lookups are done in a slightly
#        different way as described below under "REGULAR EXPRESSION
#        TABLES" or "TCP-BASED TABLES".
# 
#        By default the canonical(5) mapping affects  both  message
#        header  addresses  (i.e. addresses that appear inside mes-
#        sages) and message envelope addresses  (for  example,  the
#        addresses  that  are used in SMTP protocol commands). This
#        is controlled with the canonical_classes parameter.
# 
#        NOTE: Postfix versions 2.2 and later rewrite message head-
#        ers  from  remote  SMTP clients only if the client matches
#        the  local_header_rewrite_clients  parameter,  or  if  the
#        remote_header_rewrite_domain configuration parameter spec-
#        ifies a non-empty value. To get the behavior before  Post-
#        fix    2.2,    specify   "local_header_rewrite_clients   =
#        static:all".
# 
#        Typically, one would use the canonical(5) table to replace
#        login   names   by  Firstname.Lastname,  or  to  clean  up
#        addresses produced by legacy mail systems.
# 
#        The canonical(5) mapping is not to be confused  with  vir-
#        tual  alias  support or with local aliasing. To change the
#        destination but not the headers,  use  the  virtual(5)  or
#        aliases(5) map instead.
# 
# CASE FOLDING
#        The  search  string is folded to lowercase before database
#        lookup. As of Postfix 2.3, the search string is  not  case
#        folded  with database types such as regexp: or pcre: whose
#        lookup fields can match both upper and lower case.
# 
# TABLE FORMAT
#        The input format for the postmap(1) command is as follows:
# 
#        pattern address
#               When  pattern matches a mail address, replace it by
#               the corresponding address.
# 
#        blank lines and comments
#               Empty lines and whitespace-only lines are  ignored,
#               as  are  lines whose first non-whitespace character
#               is a `#'.
# 
#        multi-line text
#               A logical line starts with non-whitespace  text.  A
#               line  that starts with whitespace continues a logi-
#               cal line.
# 
# TABLE SEARCH ORDER
#        With lookups from indexed files such as DB or DBM, or from
#        networked   tables   such   as  NIS,  LDAP  or  SQL,  each
#        user@domain query produces a sequence of query patterns as
#        described below.
# 
#        Each  query pattern is sent to each specified lookup table
#        before trying the next query pattern,  until  a  match  is
#        found.
# 
#        user@domain address
#               Replace  user@domain  by address. This form has the
#               highest precedence.
# 
#               This is useful to clean up  addresses  produced  by
#               legacy  mail  systems.  It can also be used to pro-
#               duce Firstname.Lastname style  addresses,  but  see
#               below for a simpler solution.
# 
#        user address
#               Replace  user@site by address when site is equal to
#               $myorigin, when site is listed  in  $mydestination,
#               or   when  it  is  listed  in  $inet_interfaces  or
#               $proxy_interfaces.
# 
#               This form is useful for replacing  login  names  by
#               Firstname.Lastname.
# 
#        @domain address
#               Replace other addresses in domain by address.  This
#               form has the lowest precedence.
# 
#               Note: @domain is a wild-card.  When  this  form  is
#               applied  to  recipient  addresses, the Postfix SMTP
#               server accepts mail for any  recipient  in  domain,
#               regardless  of whether that recipient exists.  This
#               may  turn  your  mail  system  into  a  backscatter
#               source: Postfix first accepts mail for non-existent
#               recipients and then tries to return  that  mail  as
#               "undeliverable" to the often forged sender address.
# 
#               To avoid backscatter  with  mail  for  a  wild-card
#               domain, replace the wild-card mapping with explicit
#               1:1 mappings, or add a  reject_unverified_recipient
#               restriction for that domain:
# 
#                   smtpd_recipient_restrictions =
#                       ...
#                       reject_unauth_destination
#                       check_recipient_access
#                           inline:{example.com=reject_unverified_recipient}
#                   unverified_recipient_reject_code = 550
# 
#               In  the above example, Postfix may contact a remote
#               server if the recipient is rewritten  to  a  remote
#               address.
# 
# RESULT ADDRESS REWRITING
#        The lookup result is subject to address rewriting:
# 
#        o      When  the  result  has  the  form @otherdomain, the
#               result becomes the same user in otherdomain.
# 
#        o      When "append_at_myorigin=yes", append  "@$myorigin"
#               to addresses without "@domain".
# 
#        o      When "append_dot_mydomain=yes", append ".$mydomain"
#               to addresses without ".domain".
# 
# ADDRESS EXTENSION
#        When a mail address localpart contains the optional recip-
#        ient  delimiter  (e.g., user+foo@domain), the lookup order
#        becomes: user+foo@domain, user@domain, user+foo, user, and
#        @domain.
# 
#        The   propagate_unmatched_extensions   parameter  controls
#        whether an unmatched address extension  (+foo)  is  propa-
#        gated to the result of table lookup.
# 
# REGULAR EXPRESSION TABLES
#        This  section  describes how the table lookups change when
#        the table is given in the form of regular expressions. For
#        a  description  of regular expression lookup table syntax,
#        see regexp_table(5) or pcre_table(5).
# 
#        Each pattern is a regular expression that  is  applied  to
#        the entire address being looked up. Thus, user@domain mail
#        addresses are not broken up into their  user  and  @domain
#        constituent parts, nor is user+foo broken up into user and
#        foo.
# 
#        Patterns are applied in the order as specified in the  ta-
#        ble,  until  a  pattern  is  found that matches the search
#        string.
# 
#        Results are the same as with indexed  file  lookups,  with
#        the  additional feature that parenthesized substrings from
#        the pattern can be interpolated as $1, $2 and so on.
# 
# TCP-BASED TABLES
#        This section describes how the table lookups  change  when
#        lookups are directed to a TCP-based server. For a descrip-
#        tion of the TCP client/server lookup protocol, see tcp_ta-
#        ble(5).  This feature is not available up to and including
#        Postfix version 2.4.
# 
#        Each lookup operation uses the entire address once.  Thus,
#        user@domain  mail  addresses  are not broken up into their
#        user and @domain constituent parts, nor is user+foo broken
#        up into user and foo.
# 
#        Results are the same as with indexed file lookups.
# 
# BUGS
#        The  table format does not understand quoting conventions.
# 
# CONFIGURATION PARAMETERS
#        The following main.cf parameters are especially  relevant.
#        The  text  below  provides  only  a parameter summary. See
#        postconf(5) for more details including examples.
# 
#        canonical_classes  (envelope_sender,   envelope_recipient,
#        header_sender, header_recipient)
#               What  addresses  are  subject   to   canonical_maps
#               address mapping.
# 
#        canonical_maps (empty)
#               Optional  address mapping lookup tables for message
#               headers and envelopes.
# 
#        recipient_canonical_maps (empty)
#               Optional address mapping lookup tables for envelope
#               and header recipient addresses.
# 
#        sender_canonical_maps (empty)
#               Optional address mapping lookup tables for envelope
#               and header sender addresses.
# 
#        propagate_unmatched_extensions (canonical, virtual)
#               What address lookup tables copy an  address  exten-
#               sion from the lookup key to the lookup result.
# 
#        Other parameters of interest:
# 
#        inet_interfaces (all)
#               The network interface addresses that this mail sys-
#               tem receives mail on.
# 
#        local_header_rewrite_clients (permit_inet_interfaces)
#               Rewrite message header addresses in mail from these
#               clients  and  update  incomplete addresses with the
#               domain name in $myorigin or $mydomain; either don't
#               rewrite  message headers from other clients at all,
#               or rewrite message headers  and  update  incomplete
#               addresses   with   the   domain  specified  in  the
#               remote_header_rewrite_domain parameter.
# 
#        proxy_interfaces (empty)
#               The network interface addresses that this mail sys-
#               tem  receives  mail on by way of a proxy or network
#               address translation unit.
# 
#        masquerade_classes    (envelope_sender,     header_sender,
#        header_recipient)
#               What addresses are subject to address masquerading.
# 
#        masquerade_domains (empty)
#               Optional  list of domains whose subdomain structure
#               will be stripped off in email addresses.
# 
#        masquerade_exceptions (empty)
#               Optional list of user names that are not  subjected
#               to  address  masquerading,  even when their address
#               matches $masquerade_domains.
# 
#        mydestination  ($myhostname,  localhost.$mydomain,  local-
#        host)
#               The list of domains  that  are  delivered  via  the
#               $local_transport mail delivery transport.
# 
#        myorigin ($myhostname)
#               The domain name that locally-posted mail appears to
#               come from, and that locally posted mail  is  deliv-
#               ered to.
# 
#        owner_request_special (yes)
#               Enable special treatment for owner-listname entries
#               in the aliases(5) file, and don't split owner-list-
#               name  and  listname-request address localparts when
#               the recipient_delimiter is set to "-".
# 
#        remote_header_rewrite_domain (empty)
#               Don't rewrite message headers from  remote  clients
#               at all when this parameter is empty; otherwise, re-
#               write message  headers  and  append  the  specified
#               domain name to incomplete addresses.
# 
# SEE ALSO
#        cleanup(8), canonicalize and enqueue mail
#        postmap(1), Postfix lookup table manager
#        postconf(5), configuration parameters
#        virtual(5), virtual aliasing
# 
# README FILES
#        Use  "postconf  readme_directory" or "postconf html_direc-
#        tory" to locate this information.
#        DATABASE_README, Postfix lookup table overview
#        ADDRESS_REWRITING_README, address rewriting guide
# 
# LICENSE
#        The Secure Mailer license must be  distributed  with  this
#        software.
# 
# AUTHOR(S)
#        Wietse Venema
#        IBM T.J. Watson Research
#        P.O. Box 704
#        Yorktown Heights, NY 10598, USA
# 
#        Wietse Venema
#        Google, Inc.
#        111 8th Avenue
#        New York, NY 10011, USA
# 
#                                                                   CANONICAL(5)
