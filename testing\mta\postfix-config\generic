# GENERIC(5)                                                          GENERIC(5)
# 
# NAME
#        generic - Postfix generic table format
# 
# SYNOPSIS
#        postmap /etc/postfix/generic
# 
#        postmap -q "string" /etc/postfix/generic
# 
#        postmap -q - /etc/postfix/generic <inputfile
# 
# DESCRIPTION
#        The optional generic(5) table specifies an address mapping
#        that applies when mail is delivered. This is the  opposite
#        of  canonical(5)  mapping,  which  applies  when  mail  is
#        received.
# 
#        Typically, one would use the generic(5) table on a  system
#        that  does  not have a valid Internet domain name and that
#        uses  something  like  localdomain.local   instead.    The
#        generic(5)  table  is  then  used by the smtp(8) client to
#        transform local mail addresses into  valid  Internet  mail
#        addresses  when  mail  has to be sent across the Internet.
#        See the EXAMPLE section at the end of this document.
# 
#        The  generic(5)  mapping  affects  both   message   header
#        addresses (i.e. addresses that appear inside messages) and
#        message envelope addresses  (for  example,  the  addresses
#        that are used in SMTP protocol commands).
# 
#        Normally, the generic(5) table is specified as a text file
#        that serves as  input  to  the  postmap(1)  command.   The
#        result,  an  indexed file in dbm or db format, is used for
#        fast searching by the mail  system.  Execute  the  command
#        "postmap  /etc/postfix/generic" to rebuild an indexed file
#        after changing the corresponding text file.
# 
#        When the table is provided via other means  such  as  NIS,
#        LDAP  or  SQL,  the  same lookups are done as for ordinary
#        indexed files.
# 
#        Alternatively, the  table  can  be  provided  as  a  regu-
#        lar-expression  map  where  patterns  are given as regular
#        expressions, or  lookups  can  be  directed  to  TCP-based
#        server.  In those case, the lookups are done in a slightly
#        different way as described below under "REGULAR EXPRESSION
#        TABLES" or "TCP-BASED TABLES".
# 
# CASE FOLDING
#        The  search  string is folded to lowercase before database
#        lookup. As of Postfix 2.3, the search string is  not  case
#        folded  with database types such as regexp: or pcre: whose
#        lookup fields can match both upper and lower case.
# 
# TABLE FORMAT
#        The input format for the postmap(1) command is as follows:
# 
#        pattern result
#               When  pattern matches a mail address, replace it by
#               the corresponding result.
# 
#        blank lines and comments
#               Empty lines and whitespace-only lines are  ignored,
#               as  are  lines whose first non-whitespace character
#               is a `#'.
# 
#        multi-line text
#               A logical line starts with non-whitespace  text.  A
#               line  that starts with whitespace continues a logi-
#               cal line.
# 
# TABLE SEARCH ORDER
#        With lookups from indexed files such as DB or DBM, or from
#        networked   tables   such   as  NIS,  LDAP  or  SQL,  each
#        user@domain query produces a sequence of query patterns as
#        described below.
# 
#        Each  query pattern is sent to each specified lookup table
#        before trying the next query pattern,  until  a  match  is
#        found.
# 
#        user@domain address
#               Replace  user@domain  by address. This form has the
#               highest precedence.
# 
#        user address
#               Replace user@site by address when site is equal  to
#               $myorigin,  when  site is listed in $mydestination,
#               or  when  it  is  listed  in  $inet_interfaces   or
#               $proxy_interfaces.
# 
#        @domain address
#               Replace other addresses in domain by address.  This
#               form has the lowest precedence.
# 
# RESULT ADDRESS REWRITING
#        The lookup result is subject to address rewriting:
# 
#        o      When the result  has  the  form  @otherdomain,  the
#               result becomes the same user in otherdomain.
# 
#        o      When  "append_at_myorigin=yes", append "@$myorigin"
#               to addresses without "@domain".
# 
#        o      When "append_dot_mydomain=yes", append ".$mydomain"
#               to addresses without ".domain".
# 
# ADDRESS EXTENSION
#        When a mail address localpart contains the optional recip-
#        ient delimiter (e.g., user+foo@domain), the  lookup  order
#        becomes: user+foo@domain, user@domain, user+foo, user, and
#        @domain.
# 
#        The  propagate_unmatched_extensions   parameter   controls
#        whether  an  unmatched  address extension (+foo) is propa-
#        gated to the result of table lookup.
# 
# REGULAR EXPRESSION TABLES
#        This section describes how the table lookups  change  when
#        the table is given in the form of regular expressions. For
#        a description of regular expression lookup  table  syntax,
#        see regexp_table(5) or pcre_table(5).
# 
#        Each  pattern  is  a regular expression that is applied to
#        the entire address being looked up. Thus, user@domain mail
#        addresses  are  not  broken up into their user and @domain
#        constituent parts, nor is user+foo broken up into user and
#        foo.
# 
#        Patterns  are applied in the order as specified in the ta-
#        ble, until a pattern is  found  that  matches  the  search
#        string.
# 
#        Results  are  the  same as with indexed file lookups, with
#        the additional feature that parenthesized substrings  from
#        the pattern can be interpolated as $1, $2 and so on.
# 
# TCP-BASED TABLES
#        This  section  describes how the table lookups change when
#        lookups are directed to a TCP-based server. For a descrip-
#        tion of the TCP client/server lookup protocol, see tcp_ta-
#        ble(5).  This feature is not available up to and including
#        Postfix version 2.4.
# 
#        Each lookup operation uses the entire address once.  Thus,
#        user@domain mail addresses are not broken  up  into  their
#        user and @domain constituent parts, nor is user+foo broken
#        up into user and foo.
# 
#        Results are the same as with indexed file lookups.
# 
# EXAMPLE
#        The following shows a  generic  mapping  with  an  indexed
#        file.   When  mail is sent to a remote host via SMTP, this
#        replaces <EMAIL> by his  ISP  mail  address,
#        replaces  <EMAIL>  by  her ISP mail address,
#        and replaces other local addresses  by  his  ISP  account,
#        with  an address extension of +local (this example assumes
#        that the ISP supports "+" style address extensions).
# 
#        /etc/postfix/main.cf:
#            smtp_generic_maps = hash:/etc/postfix/generic
# 
#        /etc/postfix/generic:
#            <EMAIL>   <EMAIL>
#            <EMAIL>   <EMAIL>
#            @localdomain.local      <EMAIL>
# 
#        Execute the command "postmap  /etc/postfix/generic"  when-
#        ever  the table is changed.  Instead of hash, some systems
#        use dbm database files. To find out what tables your  sys-
#        tem supports use the command "postconf -m".
# 
# BUGS
#        The  table format does not understand quoting conventions.
# 
# CONFIGURATION PARAMETERS
#        The following main.cf parameters are especially  relevant.
#        The  text  below  provides  only  a parameter summary. See
#        postconf(5) for more details including examples.
# 
#        smtp_generic_maps
#               Address  mapping  lookup  table  for  envelope  and
#               header  sender and recipient addresses while deliv-
#               ering mail via SMTP.
# 
#        propagate_unmatched_extensions
#               A list of address rewriting  or  forwarding  mecha-
#               nisms  that propagate an address extension from the
#               original address to the result.   Specify  zero  or
#               more   of   canonical,   virtual,  alias,  forward,
#               include, or generic.
# 
#        Other parameters of interest:
# 
#        inet_interfaces
#               The network interface addresses  that  this  system
#               receives mail on.  You need to stop and start Post-
#               fix when this parameter changes.
# 
#        proxy_interfaces
#               Other interfaces that this machine receives mail on
#               by way of a proxy agent or network address transla-
#               tor.
# 
#        mydestination
#               List of domains that  this  mail  system  considers
#               local.
# 
#        myorigin
#               The domain that is appended to locally-posted mail.
# 
#        owner_request_special
#               Give special treatment to owner-xxx and xxx-request
#               addresses.
# 
# SEE ALSO
#        postmap(1), Postfix lookup table manager
#        postconf(5), configuration parameters
#        smtp(8), Postfix SMTP client
# 
# README FILES
#        Use  "postconf  readme_directory" or "postconf html_direc-
#        tory" to locate this information.
#        ADDRESS_REWRITING_README, address rewriting guide
#        DATABASE_README, Postfix lookup table overview
#        STANDARD_CONFIGURATION_README, configuration examples
# 
# LICENSE
#        The Secure Mailer license must be  distributed  with  this
#        software.
# 
# HISTORY
#        A genericstable feature appears in the Sendmail MTA.
# 
#        This feature is available in Postfix 2.2 and later.
# 
# AUTHOR(S)
#        Wietse Venema
#        IBM T.J. Watson Research
#        P.O. Box 704
#        Yorktown Heights, NY 10598, USA
# 
#        Wietse Venema
#        Google, Inc.
#        111 8th Avenue
#        New York, NY 10011, USA
# 
#                                                                     GENERIC(5)
