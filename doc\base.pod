
=pod

=head1 NAME

Swaks - Swiss Army Knife SMTP, the all-purpose SMTP transaction tester

=head1 DESCRIPTION

Swaks' primary design goal is to be a flexible, scriptable, transaction-oriented SMTP test tool.  It handles SMTP features and extensions such as TLS, authentication, and pipelining; multiple version of the SMTP protocol including SMTP, ESMTP, and LMTP; and multiple transport methods including UNIX-domain sockets, internet-domain sockets, and pipes to spawned processes.  Options can be specified in environment variables, configuration files, and the command line allowing maximum configurability and ease of use for operators and scripters.

=head1 QUICK START

Deliver a standard test <NAME_EMAIL> on port 25 of test-server.example.net:

=over 4

 swaks --to <EMAIL> --server test-server.example.net

=back

Deliver a standard test email, requiring CRAM-MD5 authentication <NAME_EMAIL>.  An "X-Test" header will be added to the email body.  The authentication password will be prompted for if it cannot be obtained from your L<.netrc|Net::Netrc> file.

=over 4

 swaks --to <EMAIL> --from <EMAIL> --auth CRAM-MD5 --auth-user <EMAIL> --header-X-Test "test email"

=back

Test a virus scanner using EICAR in an attachment.  Don't show the message DATA part.:

=over 4

 swaks -t <EMAIL> --attach - --server test-server.example.com --suppress-data </path/to/eicar.txt

=back

Test a spam scanner using GTUBE in the body of an email, routed via the MX records for example.com:

=over 4

 swaks --to <EMAIL> --body @/path/to/gtube/file

=back

Deliver a standard test <NAME_EMAIL> using the LMTP protocol via a UNIX domain socket file

=over 4

 swaks --to <EMAIL> --socket /var/lda.sock --protocol LMTP

=back

Report all the recipients in a text file that are non-verifiable on a test server:

=over 4

 for E in `cat /path/to/email/file`
 do
     swaks --to $E --server test-server.example.com --quit-after RCPT --hide-all
     [ $? -ne 0 ] && echo $E
 done

=back

=head1 TERMS AND CONVENTIONS

This document tries to be consistent and specific in its use of the following terms to reduce confusion.

=over 4

=item Target

The target of a transaction is the thing that Swaks connects to.  This generic term is used throughout the documentation because most other terms improperly imply something about the transport being used.

=item Transport

The transport is the underlying method used to connect to the target.

=item Transaction

A transaction is the opening of a connection over a transport to a target and using a messaging protocol to attempt to deliver a message.

=item Protocol

The protocol is the application language used to communicate with the target.  This document uses SMTP to speak generically of all three supported protocols unless it states that it is speaking of the specific 'SMTP' protocol and excluding the others.

=item Message

SMTP protocols exist to transfer messages, a set of bytes in an agreed-upon format that has a sender and a recipient.

=item Envelope

A message's envelope contains the "true" sender and receiver of a message.  It can also be referred to as its components, envelope-sender and envelope-recipients.  It is important to note that a messages envelope does not have to match its C<To:> and C<From:> headers.

=item DATA

The DATA portion of an SMTP transaction is the actual message that is being transported.  It consists of both the message's headers and its body.  DATA and body are sometimes used synonymously, but they are always two distinct things in this document.

=item Headers

A message's headers are defined as all the lines in the message's DATA section before the first blank line.  They contain information about the email that will be displayed to the recipient such as C<To:>, C<From:>, C<Subject:>, etc.  In this document headers will always be written with a capitalized first letter and a trailing colon.

=item Body

A message's body is the portion of its DATA section following the first blank line.

=item Option

An option is a flag which changes Swaks' behavior.  Always called an option regardless of how it is provided.  For instance, C<--no-data-fixup> is an option.

=item Argument

When an option takes addition data beside the option itself, that additional data is called an argument. In C<< --quit-after <stop-point>' >>, C<< <stop-point> >> is the argument to the C<--quit-after> option.

=item <literal-string>

When used in the definition of an option, text that is inside of angle brackets (C<< <> >>) indicates a descriptive label for a value that the user should provide.  For instance, C<< --quit-after <stop-point> >> indicates that C<< <stop-point> >> should be replaced with a valid stop-point value.

=item [<optional-value>]

When used in the definition of an option, text inside of square brackets ([]) indicates that the value is optional and can be omitted.  For instance, C<< --to [<recipient>] >> indicates that the C<--to> option can be used with or without a specified C<< <recipient> >>.

=back

=head1 OPTION PROCESSING

To prevent potential confusion in this document a flag to Swaks is always referred to as an "option".  If the option takes additional data, that additional data is referred to as an argument to the option.  For example, C<--from <EMAIL>> might be provided to Swaks on the command line, with C<--from> being the option and C<<EMAIL>> being C<--from>'s argument.

Options and arguments are the only way to provide information to Swaks.  If Swaks finds data during option processing that is neither an option nor an option's argument, it will error and exit.  For instance, if C<--no-data-fixup 1> were found on the command line, this would result in an error because C<--no-data-fixup> does not take an argument and therefore Swaks would not know what to do with C<1>.

Options can be given to Swaks in three ways.  They can be specified in a configuration file, in environment variables, and on the command line.  Depending on the specific option and whether an argument is given to it, Swaks may prompt the user for the argument.

When Swaks evaluates its options, it first looks for a configuration file (either in a default location or specified with C<--config>).  Then it evaluates any options in environment variables.  Finally, it evaluates command line options.  At each round of processing, any options set earlier will be overridden.  Additionally, any option can be prefixed with C<no-> to cause Swaks to forget that the variable had previously been set (either in an earlier round, or earlier in the same round).  This capability is necessary because many options treat defined-but-no-argument differently than not-defined.

As a general rule, if the same option is given multiple time, the last time it is given is the one that will be used.  This applies to both intra-method (if C<--from <EMAIL> --from <EMAIL>> is given, C<<EMAIL>> will be used) and inter-method (if C<from <EMAIL>> is given in a config file and C<--from <EMAIL>> is given on the command line, C<<EMAIL>> will be used)

Each option definition ends with a parenthetical synopsis of how the option behaves.  The following codes can be used

=over 4

=item Arg-None, Arg-Optional, Arg-Required

These three codes are mutually exclusive and describe whether or not the option takes an argument.  Note that this does not necessarily describe whether the argument is required to be specified directly, but rather whether an argument is required eventually.  For instance, C<--to> is labeled as Arg-Required, but it is legal to specify C<--to> on the command line without an argument.  This is because Swaks can prompt for the required argument if it is not directly provided.

=item From-Prompt

An option labeled with From-Prompt will prompt the user interactively for the argument if none is provided.

=item From-File

An option labeled with From-File will handle arguments as files in certain situations.

If the initial argument is C<->, the final argument is the contents of C<STDIN>.  Multiple options can all specify C<STDIN>, but the same content will be used for each of them.

If the initial argument is prefixed with C<@>, the argument will be treated as a path to a file.  The file will be opened and the contents will be used as the final argument.  If the contents of the file can't be read, Swaks will exit.  To specify a literal string value starting with an C<@>, use two C<@> symbols.  The first will be stripped.  It is not possible to include an unqualified file which starts with an C<@> sign (like C<--attach @file.txt> or C<--attach @@file.txt>), but if you include a path to the file which splits up the two C<@> signs, that will work (eg C<--attach @./@file.txt> will include the contents of the file @file.txt).

=item Sensitive

If an option marked Sensitive attempts to prompt the user for an argument and the C<--protect-prompt> option is set, Swaks will attempt to mask the user input from being echoed on the terminal.  Swaks tries to mask the input in several ways, but if none of them work program flow will continue with unmasked input.

=item Deprecated

An option labeled Deprecated has been officially deprecated and will be removed in a future release.  See the L</DEPRECATIONS> section of this documentation for details about the deprecations.

=back

The exact mechanism and format for using each of the types is listed below.

=over 4

=item CONFIGURATION FILES

A configuration file can be used to set commonly-used or abnormally verbose options.  By default, Swaks looks in order for F<$SWAKS_HOME/.swaksrc>, F<$HOME/.swaksrc>, and F<$LOGDIR/.swaksrc>.  If one of those is found to exist (and C<--config> has not been used) that file is used as the configuration file.

Additionally, a configuration file in a non-default location can be specified using C<--config>.  If this is set and not given an argument Swaks will not use any configuration file, including any default file.  If C<--config> points to a readable file, it is used as the configuration file, overriding any default that may exist.  If it points to a non-readable file an error will be shown and Swaks will exit.

A set of "portable" defaults can also be created by adding options to the end of the Swaks program file.  As distributed, the last line of Swaks should be C<__END__>.  Any lines added after C<__END__> will be treated as the contents of a configuration file.  This allows a set of user preferences to be automatically copied from server to server in a single file.

If configuration files have not been explicitly turned off, the C<__END__> config is always read.  Only one other configuration file will ever be used per single invocation of Swaks, even if multiple configuration files are specified.  If the C<__END__> config and another config are to be read, the C<__END__> config will be processed first.  Specifying the C<--config> option with no argument turns off the processing of both the C<__END__> config and any actual config files.

In a configuration file lines beginning with a hash (C<#>) are ignored.  All other lines are assumed to be an option to Swaks, with the leading dash or dashes optional.  Everything after an option line's first space is assumed to be the option's argument and is not shell processed.  Therefore, quoting is usually unneeded and will be included literally in the argument.

There is a subtle difference between providing an option with no argument and providing an option with an empty argument.  If an option line does not have a space, the entire line is treated as an option and there is no argument.  If the line ends in a single space, it will be processed as an option with an empty argument.  So, C<apt> will be treated as C<--apt>, but C<apt > will be treated as C<S<--apt ''>>.

Here is an example of the contents of a configuration file:

    # always use this sender, no matter server or logged in user
    --from <EMAIL>
    # I prefer my test emails have a pretty from header.  Note
    # the lack of dashes on option and lack of quotes around
    # entire argument.
    h-From: "Fred Example" <<EMAIL>>

Options specific to configuration file:

=over 4

=item --config [<config-file>]

This option provides a path to a specific configuration file to be used.  If specified with no argument, no automatically-found configuration file (via C<$HOME>, etc, or C<__END__>) will be processed.  If the argument is a valid file, that file will be used as the configuration file (after C<__END__> config).  If argument is not a valid, readable file, Swaks will error and exit.  This option can be specified multiple times, but only the first time it is specified (in environment variable and the command line search order) will be used. (Arg-Optional)

=back

=item CONFIGURATION ENVIRONMENT VARIABLES

Options can be supplied via environment variables.  The variables are in the form C<$SWAKS_OPT_name>, where C<name> is the name of the option that would be specified on the command line.  Because dashes aren't allowed in environment variable names in most UNIX-ish shells, no leading dashes should be used and any dashes inside the option's name should be replaced with underscores.  The following would create the same options shown in the configuration file example:

    $ SWAKS_OPT_from='<EMAIL>'
    $ SWAKS_OPT_h_From='"Fred Example" <<EMAIL>>'

Setting a variable to an empty value is the same as specifying it on the command line with no argument.  For instance, setting <SWAKS_OPT_server=""> would cause Swaks to prompt the user for the server to which to connect at each invocation.  On Windows, it is not possible to set empty environment variables.  The behavior can be simulated by setting the environment variable to C<< <> >> instead.  Additionally, embedding the header name in the header option via environment variable is not allowed on Windows (eg C<SWAKS_OPT_header_Foo=bar> will result in an error, but C<SWAKS_OPT_header="Foo: bar"> will work.)

Because there is no inherent order in options provided by setting environment variables, the options are sorted before being processed.  This is not a great solution, but it at least defines the behavior, which would be otherwise undefined.  As an example, if both C<$SWAKS_OPT_from> and C<$SWAKS_OPT_f> were set, the value from C<$SWAKS_OPT_from> would be used, because it sorts after C<$SWAKS_OPT_f>.  Also as a result of not having an inherent order in environment processing, unsetting options with the C<no-> prefix is unreliable.  It works if the option being turned off sorts before C<no->, but fails if it sorts after. Because C<no-> is primarily meant to operate between config types (for instance, unsetting from the command line an option that was set in a config file), this is not likely to be a problem.

In addition to setting the equivalent of command line options, C<$SWAKS_HOME> can be set to a directory containing the default F<.swaksrc> to be used.

=item COMMAND LINE OPTIONS

The final method of supplying options to Swaks is via the command line.  The options behave in a manner consistent with most UNIX-ish command line programs.  Many options have both a short and long form (for instance C<-s> and C<--server>).  By convention short options are specified with a single dash and long options are specified with a double-dash.  This is only a convention and either prefix will work with either type.

The following demonstrates the example shown in the configuration file and environment variable sections:

    $ swaks --from <EMAIL> --h-From: '"Fred Example" <<EMAIL>>'

=back

=head1 TRANSPORTS

Swaks can connect to a target via UNIX pipes ("pipes"), UNIX domain sockets ("UNIX sockets"), or internet domain sockets ("network sockets").  Connecting via network sockets is the default behavior.  Because of the singular nature of the transport used, each set of options in the following section is mutually exclusive.  Specifying more than one of C<--server>, C<--pipe>, or C<--socket> will result in an error.  Mixing other options between transport types will only result in the irrelevant options being ignored.  Below is a brief description of each type of transport and the options that are specific to that transport type.

=over 4

=item NETWORK SOCKETS

This transport attempts to deliver a message via TCP/IP, the standard method for delivering SMTP.  This is the default transport for Swaks.  If none of C<--server>, C<--pipe>, or C<--socket> are given then this transport is used and the target server is determined from the recipient's domain (see C<--server> below for more details).

This transport requires the L<IO::Socket::IP> module for both IPv4 and IPv6 sockets.  If this module is not loadable, Swaks will attempt to use the L<IO::Socket> library for IPv4 and L<IO::Socket::INET6> for IPv6 support.  Attempting to use this transport with none of those libraries available will result in an error and program termination.

The fall back to L<IO::Socket> and L<IO::Socket::INET6> is deprecated and will be removed in a future release.  See DEPRECATIONS below

=over 4

=item -s, --server [<target-server>[:<port>]]

Explicitly tell Swaks to use network sockets and specify the hostname or IP address to which to connect, or prompt if no argument is given.  If this option is not given and no other transport option is given, the target mail server is determined from the appropriate DNS records for the domain of the recipient email address using the L<Net::DNS> module.  If L<Net::DNS> is not available Swaks will attempt to connect to localhost to deliver.  The target port can optionally be set here.  Supported formats for this include SERVER:PORT (supporting names and IPv4 addresses); [SERVER]:PORT and SERVER/PORT (supporting names, IPv4 and IPv6 addresses).  A port set via this option will only be used if the C<--port> option is not used.  See also C<--copy-routing>. (Arg-Required, From-Prompt)

=item -p, --port [<port>]

Specify which TCP port on the target is to be used, or prompt if no argument is listed.  The argument can be a service name (as retrieved by L<getservbyname(3)>) or a port number.  The default port is smtp/25 unless influenced by the C<--protocol> or C<--tls-on-connect> options. (Arg-Required, From-Prompt)

=item -li, --local-interface [<local-interface>[:<port>]]

Use argument as the local interface for the outgoing SMTP connection, or prompt user if no argument given.  Argument can be an IP address or a hostname.  Default action is to let the operating system choose the local interface.  See C<--server> for additional comments on :<port> format.  A port set via this option will only be used if the C<--port> option is not used. (Arg-Required, From-Prompt)

=item -lp, --local-port, --lport [<port>]

Specify the outgoing port from which to originate the transaction.  The argument can be a service name (as retrieved by L<getservbyname(3)>) or a port number.  If this option is not specified the system will pick an ephemeral port.  Note that regular users cannot specify some ports. (Arg-Required, From-Prompt)

=item --copy-routing <domain>

The argument is interpreted as the domain part of an email address and it is used to find the target server using the same logic that would be used to look up the target server for a recipient email address.  See C<--to> option for more details on how the target is determined from the email domain. (Arg-Required)

=item -4, -6

Force IPv4 or IPv6. (Arg-None)

=back

=item UNIX SOCKETS

This transport method attempts to deliver messages via a UNIX-domain socket file.  This is useful for testing MTA/MDAs that listen on socket files (for instance, testing LMTP delivery to Cyrus).  This transport requires the L<IO::Socket::UNIX> module which is part of the standard Perl distribution.  If this module is not loadable, attempting to use this transport will result in an error and program termination.

=over 4

=item --socket [<socket-file>]

This option takes as its argument a UNIX-domain socket file.  If Swaks is unable to open this socket it will display an error and exit. (Arg-Required, From-Prompt)

=back

=item PIPES

This transport attempts to spawn a process and communicate with it via pipes.  The spawned program must be prepared to behave as a mail server over C<STDIN>/C<STDOUT>.  Any MTA designed to operate from inet/xinet should support this.  In addition, some MTAs provide testing modes that can be communicated with via C<STDIN>/C<STDOUT>.  This transport can be used to automate that testing.  For example, if you implemented DNSBL checking with Exim and you wanted to make sure it was working, you could run C<swaks --pipe "exim -bh *********">.  Ideally, the process you are talking to should behave exactly like an SMTP server on C<STDIN> and C<STDOUT>.  Any debugging should be sent to C<STDERR>, which will be directed to your terminal.  In practice, Swaks can generally handle some debug on the child's C<STDOUT>, but there are no guarantees on how much it can handle.

This transport requires the L<IPC::Open2> module which is part of the standard Perl distribution.  If this module is not loadable, attempting to use this transport will result in an error and program termination.

=over 4

=item --pipe [<command-and-arguments>]

Provide a process name and arguments to the process.  Swaks will attempt to spawn the process and communicate with it via pipes.  If the argument is not an executable Swaks will display an error and exit. (Arg-Required, From-Prompt)

=back

=back

=head1 PROTOCOL OPTIONS

These options are related to the protocol layer.

=over 4

=item -t, --to [<email-address>[,<email-address>[,...]]]

=item --cc [<email-address>[,<email-address>[,...]]]

=item --bcc [<email-address>[,<email-address>[,...]]]

These options all tell Swaks to use the argument(s) as the envelope-recipient for the email.  There are subtle differences between these three options, detailed below.  If any option is specified but with no arguments, Swaks will prompt the user for an argument.

C<--to> is special in that it is the only option required by Swaks. There is no default value for this option.  If no recipients are provided via any means, user will be prompted to provide one interactively.  The only exception to this is if a C<--quit-after> value is provided which will cause the SMTP transaction to be terminated before the recipient is needed.  If multiple recipients are provided and the recipient domain is needed to determine routing, the domain of the last recipient in the C<--to> argument list is used.

The primary distinction between these options is how their arguments are treated when generating the DATA portion of the email.  They each have their own replacement tokens (C<%TO_ADDRESS%>, C<%CC_ADDRESS%>, and C<%BCC_ADDRESS%> respectively) which can be used by anyone crafting a custom DATA.  In Swaks' default message, C<%TO_ADDRESS%> will be used for the To: header and, if it is populated, C<%CC_HEADER%> will be used for a Cc: header.  C<%BCC_ADDRESS%> is not used in the default DATA. (Arg-Required, From-Prompt)

=item -f, --from [<email-address>]

Use argument as envelope-sender for email, or prompt user if no argument specified.  The string C<< <> >> can be supplied to mean the null sender.  If user does not specify a sender address a default value is used.  The domain-part of the default sender is a best guess at the fully-qualified domain name of the local host.  The method of determining the local-part varies.  If the C<$LOGNAME> environment variable is set, it will be used as the local-part.  Otherwise the value from C<Win32::LoginName()> will be used on Windows and L<getpwuid(3)> on UNIX-ish platforms.  See also C<--force-getpwuid>.  If Swaks cannot determine a local hostname and the sender address is needed for the transaction, Swaks will error and exit.  In this case, a valid string must be provided via this option. (Arg-Required, From-Prompt)

=item --ehlo, --lhlo, -h, --helo [<helo-string>]

String to use as argument to HELO/EHLO/LHLO command, or prompt user if no argument is specified.  If this option is not used a best guess at the fully-qualified domain name of the local host is used.  If Swaks cannot determine a local hostname and the helo string is needed for the transaction, Swaks will error and exit.  In this case, a valid string must be provided via this option. (Arg-Required, From-Prompt)

=item -q, --quit, --quit-after <stop-point>

Point at which the transaction should be stopped.  When the requested stopping point is reached in the transaction, and provided that Swaks has not errored out prior to reaching it, Swaks will send "QUIT" and attempt to close the connection cleanly.  These are the valid arguments and notes about their meaning. (Arg-Required)

=over 4

=item PROXY

Quit after the server sends a response to a PROXY request.  Note that if there is not an error negotiating proxy, this will be synonymous with CONNECT.

=item CONNECT, BANNER

Terminate the session after receiving the greeting banner from the target.

=item FIRST-HELO, FIRST-EHLO, FIRST-LHLO

In a STARTTLS (but not tls-on-connect) session, terminate the transaction after the first of two HELOs.  In a non-STARTTLS transaction, behaves the same as HELO (see below).

=item XCLIENT

Quit after XCLIENT is negotiation. This always quits after the point where XCLIENT would have been negotiated, regardless of whether it was attempted.

=item XCLIENT-HELO

Quit after the HELO that XCLIENT negotiation triggers. This differs from HELO and FIRST-HELO because XCLIENT negotiation can happen at multiple points in the SMTP transaction and it is impossible to specifically refer to the XCLIENT-triggered HELO using the HELO or FIRST-HELO stop-points. This always quits after the point where the XCLIENT-triggered HELO would have occurred, regardless of whether it was attempted.

=item STARTTLS, TLS

Quit the transaction immediately following TLS negotiation.  Note that this happens in different places depending on whether STARTTLS or tls-on-connect are used.  This always quits after the point where TLS would have been negotiated, regardless of whether it was attempted.

=item HELO, EHLO, LHLO

In a STARTTLS or XCLIENT session, quit after the second HELO.  Otherwise quit after the first and only HELO.

=item AUTH

Quit after authentication.  This always quits after the point where authentication would have been negotiated, regardless of whether it was attempted.

=item MAIL, FROM

Quit after MAIL FROM: is sent.

=item RCPT, TO

Quit after RCPT TO: is sent.

=back

=item --da, --drop-after <stop-point>

The option is similar to C<--quit-after>, but instead of trying to cleanly shut down the session it simply terminates the session.  This option accepts the same stop-points as C<--quit-after> and additionally accepts DATA and DOT, detailed below. (Arg-Required)

=over 4

=item DATA

Drop the connection after DATA is sent by server.

=item DOT

Drop the connection after the final '.' of the message is sent by server.

=back

=item --das, --drop-after-send <stop-point>

This option is similar to C<--drop-after>, but instead of dropping the connection after reading a response to the stop-point, it drops the connection immediately after sending stop-point.  It accepts the same stop-points as C<--drop-after>. If the stop-point is for an optional part of the transaction which is not actually sent (for instance STARTTLS or AUTH), this option will behave identically to C<--drop-after>. See below for specific details. (Arg-Required)

=over 4

=item CONNECT

Connect to the server and then drops the connection before receiving the server's banner.

=item STARTTLS, TLS

Behaves identically to C<--drop-after>.

=item HELO, EHLO, LHLO

Doesn't necessarily work as expected.  If it appears to read the HELO response incorrectly, use FIRST-HELO instead.

=back

=item --timeout [<time>]

Use argument as the SMTP transaction timeout, or prompt user if no argument given.  Argument can either be a pure digit, which will be interpreted as seconds, or can have a specifier s, m, or h (5s = 5 seconds, 3m = 180 seconds, 1h = 3600 seconds).  As a special case, 0 means don't timeout the transactions.  Default value is 30s. (Arg-Required, From-Prompt)

=item --protocol <protocol>

Specify which protocol to use in the transaction.  Valid options are shown in the table below.  Currently the 'core' protocols are SMTP, ESMTP, and LMTP.  By using variations of these protocol types one can tersely specify default ports, whether authentication should be attempted, and the type of TLS connection that should be attempted.  The default protocol is ESMTP.  The following table demonstrates the available arguments to C<--protocol> and the options each sets as a side effect.  (Arg-Required)

=over 4

=item SMTP

HELO, "-p 25"

=item SSMTP

EHLO-E<gt>HELO, "-tlsc -p 465"

=item SSMTPA

EHLO-E<gt>HELO, "-a -tlsc -p 465"

=item SMTPS

HELO, "-tlsc -p 465"

=item ESMTP

EHLO-E<gt>HELO, "-p 25"

=item ESMTPA

EHLO-E<gt>HELO, "-a -p 25"

=item ESMTPS

EHLO-E<gt>HELO, "-tls -p 25"

=item ESMTPSA

EHLO-E<gt>HELO, "-a -tls -p 25"

=item LMTP

LHLO, "-p 24"

=item LMTPA

LHLO, "-a -p 24"

=item LMTPS

LHLO, "-tls -p 24"

=item LMTPSA

LHLO, "-a -tls -p 24"

=back

=item --pipeline

If the remote server supports it, attempt SMTP PIPELINING (RFC 2920). (Arg-None)

=item --prdr

If the server supports it, attempt Per-Recipient Data Response (PRDR) (L<https://tools.ietf.org/html/draft-hall-prdr-00.txt>).  PRDR is not yet standardized, but MTAs have begun implementing the proposal. (Arg-None)

=item --force-getpwuid

Tell Swaks to use the system-default method of determining the current user's username for the default sender local-part instead of trying C<$LOGNAME> first.  Despite the UNIX-ish-specific option name, this option also works on Windows. (Arg-None)

=back

=head1 TLS / ENCRYPTION

These are options related to encrypting the transaction.  These have been tested and confirmed to work with all three transport methods.  The L<Net::SSLeay> module is used to perform encryption when it is requested.  If this module is not loadable Swaks will either ignore the TLS request or error out, depending on whether the request was optional.  STARTTLS is defined as an extension in the ESMTP protocol and will be unavailable if C<--protocol> is set to a variation of plain SMTP.  Because it is not defined in the protocol itself, C<--tls-on-connect> is available for any protocol type if the target supports it.

A local certificate is not required for a TLS connection to be negotiated.  However, some servers use client certificate checking to verify that the client is allowed to connect.  Swaks can be told to use a specific local certificate using the C<--tls-cert> and C<--tls-key> options, and optionally to use a certificate chain using the C<--tls-chain> option.

=over 4

=item -tls

Require connection to use STARTTLS.  Exit if TLS not available for any reason (not advertised, negotiations failed, etc). (Arg-None)

=item -tlso, --tls-optional

Attempt to use STARTTLS if available, continue with normal transaction if TLS was unable to be negotiated for any reason.  Note that this is a semi-useless option as currently implemented because after a negotiation failure the state of the connection is unknown.  In some cases, like a version mismatch, the connection should be left as plaintext.  In others, like a verification failure, the server-side may think that it should continue speaking TLS while the client thinks it is plaintext.  There may be an attempt to add more granular state detection in the future, but for now just be aware that odd things may happen with this option if the TLS negotiation is attempted and fails. (Arg-None)

=item -tlsos, --tls-optional-strict

Attempt to use STARTTLS if available.  Proceed with transaction if TLS is negotiated successfully or STARTTLS not advertised.  If STARTTLS is advertised but TLS negotiations fail, treat as an error and abort transaction.  Due to the caveat noted above, this is a much saner option than C<--tls-optional>. (Arg-None)

=item -tlsc, --tls-on-connect

Initiate a TLS connection immediately on connection.  Following common convention, if this option is specified the default port changes from 25 to 465, though this can still be overridden with the --port option. (Arg-None)

=item -tlsp, --tls-protocol <tls-version-specification>

Specify which protocols to use (or not use) when negotiating TLS.  At the time of this writing, the available protocols are sslv2, sslv3, tlsv1, tlsv1_1, tlsv1_2, and tlsv1_3.  The availability of these protocols is dependent on your underlying OpenSSL library, so not all of these may be available.  The list of available protocols is shown in the output of C<--dump> (assuming TLS is available at all).

The specification string is a comma-delimited list of protocols that can be used or not used.  For instance 'tlsv1,tlsv1_1' will only succeed if one of those two protocols is available on both the client and the server.  Conversely, 'no_sslv2,no_sslv3' will attempt to negotiate any protocol except sslv2 and sslv3.  The two forms of specification cannot be mixed. (Arg-Required)

=item --tls-cipher <cipher-string>

The argument to this option is passed to the underlying OpenSSL library to set the list of acceptable ciphers to the be used for the connection.  The format of this string is opaque to Swaks and is defined in L<https://www.openssl.org/docs/manmaster/man1/openssl-ciphers.html#CIPHER-LIST-FORMAT>.  A brief example would be C<--tls-cipher '3DES:+RSA'>. (Arg-Required)

=item --tls-verify

Tell Swaks to attempt to verify the server's certificate.  This option is identical to specifying both the C<--tls-verify-ca> and C<--tls-verify-host> options.  See those options for detailed descriptions of how to fine-tune each type of verification.

By default, TLS verification is not required.  If TLS verification is required by C<--tls-verify>, C<--tls-verify-ca>, or C<--tls-verify-host> and the requested type of verification fails, TLS negotiation will not succeed. (Arg-None)

=item --tls-verify-ca

Require that the server's certificate be signed by a known certificate authority and not be expired.  By default the list of known CAs will be whatever is available via the client Swaks is running on.  To provide a custom CA, see C<--tls-ca-path>. (Arg-None)

=item --tls-verify-host

Require that the target of the current connection be listed in the server certificate's Subject Alternative Name (SAN) or Subject CommonName (CN).

The target that Swaks uses for verification will vary.  It can be a hostname, either provided directly via the C<--server> option or looked up via MX records.  In this case, verification performs as expected.  If the target is an IP, the IP will be looked up in the certificate, which is possible but unusual.  If the transport is C<--pipe> or C<--socket>, there will not be a meaningful target to verify in the certificate and verification will fail.  In this situation it's better to use only C<--tls-verify-ca> or to override the target used for verification with the C<--tls-verify-target> option. (Arg-None)

=item --tls-verify-target <verification-string>

When set, the argument to this option will be used as the host to be verified for C<--tls-verify-host>.  This is necessary when using C<--tls-verify-host> with either the C<--pipe> or C<--socket> transports, which do not have a verifiable target by default.  It can also be used to override the default target lookup when using the C<--server> transport.  For instance, it can be used to verify that the certificate of a server explicitly connect to via IP contains a specific certificate. (Arg-Required)

=item --tls-ca-path <ca-location>

Specify an alternate location for CA information for verifying server certificates.  The argument can point to a file or directory.  The default behavior is to use the underlying OpenSSL library's default information. (Arg-Required)

=item --tls-cert <cert-file>

Provide a path to a file containing the local certificate Swaks should use if TLS is negotiated.  If a certificate chain needs to be provided, it can be provided via this file or via a separate file with C<--tls-chain>.  The file path argument is required.  As currently implemented the certificate in the file must be in PEM format.  Contact the author if there's a compelling need for ASN1.  If this option is set, C<--tls-key> is also required. (Arg-Required)

=item --tls-key <key-file>

Provide a path to a file containing the local private key Swaks should use if TLS is negotiated.  The file path argument is required.  As currently implemented the certificate in the file must be in PEM format.  Contact the author if there's a compelling need for ASN1.  If this option is set, C<--tls-cert> is also required. (Arg-Required)

=item --tls-chain <chain-file>

Provide a path to a file containing the local certificate chain Swaks should use if TLS is negotiated.  The file path argument is required.  As currently implemented the certificate in the file must be in PEM format.  Contact the author if there's a compelling need for ASN1.  If this option is set, C<--tls-cert> and C<--tls-key> are also required. (Arg-Required)

=item --tls-get-peer-cert [<output-file>]

Get a copy of the TLS peer's certificate.  If no argument is given, it will be displayed to C<STDOUT>.  If an argument is given it is assumed to be a filesystem path specifying where the certificate should be written.  The saved certificate can then be examined using standard tools such as the openssl command.  If a file is specified its contents will be overwritten.  This option will only ever return one certificate.  In order to get every certificate sent by the server, see C<--tls-get-peer-chain>. (Arg-Optional)

=item --tls-get-peer-chain [<output-file>]

Get a copy of the TLS certificate chain sent by the server.  If no argument is given, it will be displayed to C<STDOUT>.  If an argument is given it is assumed to be a filesystem path specifying where the certificate should be written.  The saved chain can then be examined using standard tools such as the openssl command.  If a file is specified its contents will be overwritten. See also C<--tls-get-peer-cert>. (Arg-Optional)

=item --tls-sni <sni-string>

Specify the Server Name Indication field to send when the TLS connection is initiated. (Arg-Required)

=back

=head1 AUTHENTICATION

Swaks will attempt to authenticate to the target mail server if instructed to do so.  This section details available authentication types, requirements, options and their interactions, and other fine points in authentication usage.  Because authentication is defined as an extension in the ESMTP protocol it will be unavailable if C<--protocol> is set to a variation of SMTP.

All authentication methods require base64 encoding.  If the L<MIME::Base64> Perl module is loadable Swaks attempts to use it to perform these encodings.  If L<MIME::Base64> is not available Swaks will use its own onboard base64 routines.  These are slower than the L<MIME::Base64> routines and less reviewed, though they have been tested thoroughly.  Using the L<MIME::Base64> module is encouraged.

If authentication is required (see options below for when it is and isn't required) and the requirements aren't met for the authentication type available, Swaks displays an error and exits.  Two ways this can happen include forcing Swaks to use a specific authentication type that Swaks can't use due to missing requirements, or allowing Swaks to use any authentication type, but the server only advertises types Swaks can't support.  In the former case Swaks errors out at option processing time since it knows up front it won't be able to authenticate.  In the latter case Swaks will error out at the authentication stage of the SMTP transaction since Swaks will not be aware that it will not be able to authenticate until that point.

Following are the supported authentication types including any individual notes and requirements.

The following options affect Swaks' use of authentication.  These options are all inter-related.  For instance, specifying C<--auth-user> implies C<--auth> and C<--auth-password>.  Specifying C<--auth-optional> implies C<--auth-user> and C<--auth-password>, etc.

=over 4

=item -a, --auth [<auth-type>[,<auth-type>[,...]]]

Require Swaks to authenticate.  If no argument is given, any supported auth-types advertised by the server are tried until one succeeds or all fail.  If one or more auth-types are specified as an argument, each that the server also supports is tried in order until one succeeds or all fail.  This option requires Swaks to authenticate, so if no common auth-types are found or no credentials succeed, Swaks displays an error and exits. (Arg-Optional)

The following tables lists the valid auth-types

=over 4

=item LOGIN, PLAIN

These basic authentication types are fully supported and tested and have no additional requirements.

=item LOGIN-INITIAL

This is a variation of the LOGIN auth-type where the username is sent to the server as as part of the initial-response argument to the AUTH LOGIN request.  See L<https://www.rfc-editor.org/rfc/rfc4954#page-3>.

=item CRAM-MD5

The CRAM-MD5 authenticator requires the L<Digest::MD5> module.  It is fully tested and believed to work against any server that implements it.

=item DIGEST-MD5

The DIGEST-MD5 authenticator (RFC2831) requires the Authen::SASL module.  Version 20100211.0 and earlier used L<Authen::DigestMD5> which had some protocol level errors which prevented it from working with some servers.  L<Authen::SASL>'s DIGEST-MD5 handling is much more robust.

The DIGEST-MD5 implementation in Swaks is fairly immature.  It currently supports only the "auth" qop type, for instance.  If you have DIGEST-MD5 experience and would like to help Swaks support DIGEST-MD5 better, please get in touch with me.

The DIGEST-MD5 protocol's "realm" value can be set using the C<--auth-extra> "realm" keyword.  If no realm is given, a reasonable default will be used.

The DIGEST-MD5 protocol's "digest-uri" values can be set using the C<--auth-extra> option.  For instance, you could create the digest-uri-value of "lmtp/mail.example.com/example.com" with the option C<--auth-extra dmd5-serv-type=lmtp,dmd5-host=mail.example.com,dmd5-serv-name=example.com>.  The "digest-uri-value" string and its components is defined in RFC2831.  If none of these values are given, reasonable defaults will be used.

=item CRAM-SHA1

The CRAM-SHA1 authenticator requires the L<Digest::SHA> module.  This type has only been tested against a non-standard implementation on an Exim server and may therefore have some implementation deficiencies.

=item NTLM/SPA/MSN

These authenticators require the Authen::NTLM module.  This type has been tested against Exim, Communigate, and Exchange 2007.

In addition to the standard username and password, this authentication type can also recognize a "domain".  The domain can be set using the C<--auth-extra> "domain" keyword.  Note that this has never been tested with a mail server that doesn't ignore DOMAIN so this may not be implemented incorrectly.

=back

=item -ao, --auth-optional [<auth-type>[,<auth-type>[,...]]]

This option behaves identically to C<--auth> except that it requests authentication rather than requiring it.  If no common auth-types are found or no credentials succeed, Swaks proceeds as if authentication had not been requested. (Arg-Optional)

=item -aos, --auth-optional-strict [<auth-type>[,<auth-type>[,...]]]

This option is a compromise between C<--auth> and C<--auth-optional>.  If authentication is never attempted (server doesn't advertise authentication or no common authentication types are found), it behaves like C<--auth-optional> and the smtp transaction continues.  If authentication is attempted but fails, it behaves like C<--auth> and exits with an error. (Arg-Optional)

=item -au, --auth-user [<username>]

Provide the username to be used for authentication.  If no username is provided, indicate that Swaks should attempt to find the username via F<.netrc> (requires the L<Net::Netrc> module).  If no username is provided and cannot be found via F<.netrc>,  the user will be prompted to provide one.  The string C<< <> >> can be supplied to mean an empty username. (Arg-Required, From-Prompt)

=item -ap, --auth-password [<password>]

Provide the password to be used for authentication. If no password is provided, indicate that Swaks should attempt to find the password via F<.netrc> (requires the L<Net::Netrc> module).  If no password is provided and cannot be found via F<.netrc>,  the user will be prompted to provide one.  The string C<< <> >> can be supplied to mean an empty password. (Arg-Required, From-Prompt, Sensitive)

=item -ae, --auth-extra <key-value-pair>[,<key-value-pair>[,...]]

Some of the authentication types allow extra information to be included in the authentication process.  Rather than add a new option for every nook and cranny of each authenticator, the C<--auth-extra> option allows this information to be supplied.  The format for <key-value-pair> is KEYWORD=VALUE. (Arg-Required)

The following table lists the currently recognized keywords and the authenticators that use them

=over 4

=item realm, domain

The realm and domain keywords are synonymous.  Using either will set the "domain" option in NTLM/MSN/SPA and the "realm" option in DIGEST-MD5

=item dmd5-serv-type

The dmd5-serv-type keyword is used by the DIGEST-MD5 authenticator and is used, in part, to build the digest-uri-value string (see RFC2831)

=item dmd5-host

The dmd5-host keyword is used by the DIGEST-MD5 authenticator and is used, in part, to build the digest-uri-value string (see RFC2831)

=item dmd5-serv-name

The dmd5-serv-name keyword is used by the DIGEST-MD5 authenticator and is used, in part, to build the digest-uri-value string (see RFC2831)

=back

=item -am, --auth-map <key-value-pair>[,<key-value-pair>[,...]]

Provides a way to map alternate names onto base authentication types.  Useful for any sites that use alternate names for common types.  The format for <key-value-pair> is AUTH-ALIAS=AUTH-TYPE.  This functionality is actually used internally to map types SPA and MSN onto the base type NTLM.  The command line argument to simulate this would be C<--auth-map SPA=NTLM,MSN=NTLM>.  All of the auth-types listed above are valid targets for mapping except SPA and MSN. (Arg-Required)

=item -apt, --auth-plaintext

Instead of showing AUTH strings base64 encoded as they are transmitted, translate them to plaintext before printing on screen. (Arg-None)

=item -ahp, --auth-hide-password [<replacement-string>]

If this option is specified, any time a readable password would be printed to the terminal (specifically AUTH PLAIN and AUTH LOGIN) the password is replaced with the string 'PROVIDED_BUT_REMOVED' (or the contents of <replacement-string> if provided).  The dummy string may or may not be base64 encoded, contingent on the C<--auth-plaintext> option.

Note that C<--auth-hide-password> is similar, but not identical, to the C<--protect-prompt> option.  The former protects passwords from being displayed in the SMTP transaction regardless of how they are entered.  The latter protects sensitive strings when the user types them at the terminal, regardless of how the string would be used. (Arg-Optional)

=back

=head1 XCLIENT OPTIONS

XCLIENT is an SMTP extension introduced by the Postfix project.  XCLIENT allows a (properly-authorized) client to tell a server to use alternate information, such as IP address or hostname, for the client.  This allows much easier paths for testing mail server configurations.  Full details on the protocol are available at L<http://www.postfix.org/XCLIENT_README.html>.

The XCLIENT verb can be passed to the server multiple times per SMTP session with different attributes.  For instance, HELO and PROTO might be passed in one call and NAME and ADDR passed in a second. Because it can be useful for testing, Swaks exposes some control over how the attributes are grouped and in what order they are passed to the server. The different options attempt to expose simplicity for those using Swaks as a client, and complexity for those using Swaks to test installs.

=over 4

=item --xclient-addr [<string>]

=item --xclient-name [<string>]

=item --xclient-port [<string>]

=item --xclient-proto [<string>]

=item --xclient-destaddr [<string>]

=item --xclient-destport [<string>]

=item --xclient-helo [<string>]

=item --xclient-login [<string>]

=item --xclient-reverse-name [<string>]

These options specify XCLIENT attributes that should be sent to the target server.  If <string> is not provided, Swaks will prompt and read the value on C<STDIN>.  See L<http://www.postfix.org/XCLIENT_README.html> for official documentation for what the attributes mean and their possible values, including the special "[UNAVAILABLE]" and "[TEMPUNAVAIL]" values.

By way of simple example, setting C<--xclient-name foo.example.com --xclient-addr ***********> will cause Swaks to send the SMTP command C<XCLIENT NAME=foo.example.com ADDR=***********>.

Note that the "REVERSE_NAME" attribute doesn't seem to appear in the official documentation.  There is a mailing list thread that documents it, viewable at L<http://comments.gmane.org/gmane.mail.postfix.user/192623>.

These options can all be mixed with each other, and can be mixed with the C<--xclient> option (see below). By default all attributes will be combined into one XCLIENT call, but see C<--xclient-delim>. (Arg-Required, From-Prompt)

=item --xclient-delim

When this option is specified, it indicates a break in XCLIENT attributes to be sent.  For instance, setting C<--xclient-helo 'helo string' --xclient-delim --xclient-name foo.example.com --xclient-addr ***********> will cause Swaks to send two XCLIENT calls, C<XCLIENT HELO=helo+20string> and C<XCLIENT NAME=foo.example.com ADDR=***********>.  This option is ignored where it doesn't make sense (at the start or end of XCLIENT options, by itself, consecutively, etc). (Arg-None)

=item --xclient [<string>]

This is the "free form" XCLIENT option.  Whatever value is provided for <string> will be sent verbatim as the argument to the XCLIENT SMTP command.  For example, if C<--xclient 'NAME= ADDR=*********** FOO=bar'> is used, Swaks will send the SMTP command C<XCLIENT NAME= ADDR=*********** FOO=bar>.  If no argument is passed on command line, Swaks will prompt and read the value on STDIN.

The primary advantage to this over the more specific options above is that there is no XCLIENT syntax validation here.  This allows you to send invalid XCLIENT to the target server for testing.  Additionally, at least one MTA (Message Systems' Momentum, formerly ecelerity) implements XCLIENT without advertising supported attributes.  The C<--xclient> option allows you to skip the "supported attributes" check when communicating with this type of MTA (though see also C<--xclient-no-verify>).

The C<--xclient> option can be mixed freely with the C<--xclient-*> options above.  The argument to C<--xclient> will be sent in its own command group.  For instance, if C<--xclient-addr *********** --xclient-port 26 --xclient 'FOO=bar NAME=wind'> is given to Swaks, C<XCLIENT ADDR=*********** PORT=26> and C<XCLIENT FOO=bar NAME=wind> will both be sent to the target server. (Arg-Required, From-Prompt)

=item --xclient-no-verify

Do not enforce the requirement that an XCLIENT attribute must be advertised by the server in order for Swaks to send it in an XCLIENT command.  This is to support servers which don't advertise the attributes but still support them. (Arg-None)

=item --xclient-before-starttls

If Swaks is configured to attempt both XCLIENT and STARTTLS, it will do STARTTLS first.  If this option is specified it will attempt XCLIENT first. (Arg-None)

=item --xclient-optional

=item --xclient-optional-strict

In normal operation, setting one of the C<--xclient*> options will require a successful XCLIENT transaction to take place in order to proceed (that is, XCLIENT needs to be advertised, all the user-requested attributes need to have been advertised, and the server needs to have accepted Swaks' XCLIENT request).  These options change that behavior.  C<--xclient-optional> tells Swaks to proceed unconditionally past the XCLIENT stage of the SMTP transaction, regardless of whether it was successful.  C<--xclient-optional-strict> is similar but more granular.  The strict version will continue to XCLIENT was not advertised, but will fail if XCLIENT was attempted but did not succeed. (Arg-None)

=back

=head1 PROXY OPTIONS

Swaks implements the Proxy protocol as defined in L<http://www.haproxy.org/download/1.5/doc/proxy-protocol.txt>.  Proxy allows an application load balancer, such as HAProxy, to be used in front of an MTA while still allowing the MTA access to the originating host information.  Proxy support in Swaks allows direct testing of an MTA configured to expect requests from a proxy, bypassing the proxy itself during testing.

Swaks makes no effort to ensure that the Proxy options used are internally consistent.  For instance, C<--proxy-family> (in version 1) is expected to be one of "TCP4" or "TCP6".  While it will likely not make sense to the target server, Swaks makes no attempt to ensure that C<--proxy-source> and C<--proxy-dest> are in the same protocol family as C<--proxy-family> or each other.

The C<--proxy> option is mutually exclusive with all other C<--proxy-*> options except C<--proxy-version>.

When C<--proxy> is not used, all of C<--proxy-family>, C<--proxy-source>, C<--proxy-source-port>, C<--proxy-dest>, and C<--proxy-dest-port> are required.  Additionally, when C<--proxy-version> is 2, C<--proxy-protocol> and C<--proxy-command> are optional.

=over 4

=item --proxy-version [ 1 | 2 ]

Whether to use version 1 (human readable) or version 2 (binary) of the Proxy protocol.  Version 1 is the default.  Version 2 is only implemented through the "address block", and is roughly on par with the information provided in version 1. (Arg-Required, From-Prompt)

=item --proxy [<string>]

This option provides the raw proxy string which will be sent to the server.  The protocol prefix ("PROXY " for version 1, the 12-byte protocol header for version 2) can be present or not in the argument.  This option allows sending incomplete or malformed Proxy strings to a target server for testing.  This option is mutually exclusive with all other C<--proxy-*> options which provide granular proxy information.

Because version 2 of the Proxy protocol is a binary protocol, there are multiple ways to provide the argument to this option.  If the argument starts with "base64:", that prefix is stripped and the rest of the string is base64 decoded before use.  If the argument starts with C<@> it will be treated as a filename and the proxy value will be read from the file.  Any other value is assumed to be the literal value for the proxy string. (Arg-Required, From-Prompt, From-File)

=item --proxy-family [<string>]

For version 1, specifies both the address family and transport protocol.  The protocol defines TCP4 and TCP6.

For version 2, specifies only the address family.  The protocol defines AF_UNSPEC, AF_INET, AF_INET6, and AF_UNIX. (Arg-Required, From-Prompt)

=item --proxy-protocol [<string>]

For version 2, specifies the transport protocol.  The protocol defines UNSPEC, STREAM, and DGRAM.  The default is STREAM.  This option is unused in version 1. (Arg-Required, From-Prompt)

=item --proxy-command [<string>]

For version 2, specifies the transport protocol.  The protocol defines LOCAL and PROXY.  The default is PROXY.  This option is unused in version 1. (Arg-Required, From-Prompt)

=item --proxy-source [<string>]

Specify the source address of the proxied connection. (Arg-Required, From-Prompt)

=item --proxy-source-port [<string>]

Specify the source port of the proxied connection. (Arg-Required, From-Prompt)

=item --proxy-dest [<string>]

Specify the destination address of the proxied connection. (Arg-Required, From-Prompt)

=item --proxy-dest-port [<string>]

Specify the destination port of the proxied connection. (Arg-Required, From-Prompt)

=back

=head1 DATA OPTIONS

These options pertain to the contents for the DATA portion of the SMTP transaction.  By default a very simple message is sent.  If the C<--attach> or C<--attach-body> options are used, Swaks attempts to upgrade to a MIME message.

=over 4

=item -d, --data [<data-portion>]

Use argument as the entire contents of DATA.

If no argument is provided, user will be prompted to supply value.

If the argument C<-> is provided the data will be read from C<STDIN> with no prompt.

If the argument starts with C<@> it will be treated as a filename.  If you would like to pass in an argument that starts with C<@> and isn't a filename, prefix the argument with an additional C<@>.  For example, C<@file.txt> will force processing of F<file.txt>.  C<@@data> will use the string '@data'.

If the argument does not contain any literal (0x0a) or representative (0x5c, 0x6e or %NEWLINE%) newline characters, it will be treated as a filename.  If the file is open-able, the contents of the file will be used as the data portion.  If the file cannot be opened, Swaks will error and exit.  The entire behavior described in this paragraph is deprecated and will be removed in a future release.  Instead use a leading C<@> to explicitly set that the argument is a filename.

Any other argument will be used as the DATA contents.

The value can be on one single line, with C<\n> (ASCII 0x5c, 0x6e) representing where line breaks should be placed.  Leading dots will be quoted.  Closing dot is not required but is allowed.  The default value for this option is C<< Date: %DATE%\nTo: %TO_ADDRESS%\nFrom: %FROM_ADDRESS%\nSubject: test %DATE%\nMessage-Id: <%MESSAGEID%>\nX-Mailer: swaks v%SWAKS_VERSION% jetmore.org/john/code/swaks/\n%NEW_HEADERS%\n%BODY%\n >>.

Very basic token parsing is performed on the DATA portion.  The following table shows the recognized tokens and their replacement values. (Arg-Required, From-Prompt, From-File)

=over 4

=item %FROM_ADDRESS%, ..FROM_ADDRESS..

Replaced with the envelope-sender.

=item %TO_ADDRESS%, ..TO_ADDRESS..

Replaced with the envelope-recipient(s) set by the C<--to> option.

=item %CC_ADDRESS%, ..CC_ADDRESS..

Replaced with the envelope-recipient(s) set by the C<--cc> option.

=item %BCC_ADDRESS%, ..BCC_ADDRESS..

Replaced with the envelope-recipient(s) set by the C<--bcc> option.

=item %DATE%, ..DATE..

Replaced with the current time in a format suitable for inclusion in the Date: header.  Note this attempts to use the standard module L<POSIX> for timezone calculations.  If this module is unavailable or the current environment doesn't support the %z strftime format token (as on Windows) the date string will be in GMT.

=item %MESSAGEID%, ..MESSAGEID..

Replaced with a message ID string suitable for use in a Message-Id header.  The value for this token will remain consistent for the life of the process.

=item %SWAKS_VERSION%, ..SWAKS_VERSION..

Replaced with the version of the currently-running Swaks process.

=item %NEW_HEADERS%, ..NEW_HEADERS..

Replaced with the contents of the C<--add-header> option.  If C<--add-header> is not specified this token is simply removed.

=item %BODY%, ..BODY..

Replaced with the value specified by the C<--body> option.  See C<--body> for default.

=item %NEWLINE%, ..BODY..

Replaced with carriage return, newline (0x0d, 0x0a).  This is identical to using C<\n> (0x5c, 0x6e), but doesn't have the escaping concerns that the backslash can cause on the newline.

=back

=item -dab, --dump-as-body [<section>[,<section>[,...]]]

If C<--dump-as-body> is used and no other option is used to change the default body of the message, the body is replaced with output similar to the output of what is provided by C<--dump>.  C<--dump>'s initial program capability stanza is not displayed, and the "data" section is not included.  Additionally, C<--dump> always includes passwords.  By default C<--dump-as-body> does not include passwords, though this can be changed with C<--dump-as-body-shows-password>.  C<--dump-as-body> takes the same arguments as C<--dump> except the SUPPORT and DATA arguments are not supported. (Arg-Optional)

=item -dabsp, --dump-as-body-shows-password

Cause C<--dump-as-body> to include plaintext passwords.  This option is not recommended.  This option implies C<--dump-as-body>. (Arg-None)

=item --body [<body-specification>]

Specify the body of the email.  The default is "This is a test mailing".  If no argument to C<--body> is given, prompt to supply one interactively.  If C<-> is supplied, the body will be read from standard input.  Arguments beginning with C<@> will be treated as filenames containing the body data to use (see C<--data> for more detail).

If, after the above processing, the argument represents an open-able file, the content of that file is used as the body.  This is deprecated behavior and will be removed in a future release.  Instead use a leading C<@> to explicitly set that the argument is a filename.

If the message is forced to MIME format (see C<--attach>) C<--body 'body text'> is the same as C<--attach-type text/plain --attach-body 'body text'>.  See C<--attach-body> for details on creating a multipart/alternative body. (Arg-Required, From-Prompt, From-File)

=item --attach [<attachment-specification>]

When one or more C<--attach> option is supplied, the message is changed into a multipart/mixed MIME message.  The arguments to C<--attach> are processed the same as C<--body> with respect to C<STDIN>, file contents, etc.  C<--attach> can be supplied multiple times to create multiple attachments.  By default, each attachment is attached as an application/octet-stream file.  See C<--attach-type> for changing this behavior.

If the contents of the attachment are provided via a file name, the MIME encoding will include that file name.  See C<--attach-name> for more detail on file naming.

It is legal for C<-> (C<STDIN>) to be specified as an argument multiple times (once for C<--body> and multiple times for C<--attach>).  In this case, the same content will be attached each time it is specified.  This is useful for attaching the same content with multiple MIME types. (Arg-Required, From-File)

=item --attach-body [<body-specification>]

This is a variation on C<--attach> that is specifically for the body part of the email.  It behaves identically to C<--attach> in that it takes the same arguments and forces the creation of a MIME message.  However, it is different in that the argument will always be the first MIME part in the message, no matter where in option processing order it is encountered.  Additionally, C<--attach-body> options stack to allow creation of multipart/alternative bodies.  For example, C<--attach-type text/plain --attach-body 'plain text body' --attach-type text/html --attach-body 'html body'> would create a multipart/alternative message body. (Arg-Required, From-File)

=item --attach-type <mime-type>

By default, content that gets MIME attached to a message with the C<--attach> option is encoded as application/octet-stream (except for the body, which is text/plain by default).  C<--attach-type> changes the mime type for every C<--attach> option which follows it.  It can be specified multiple times.  The current MIME type gets reset to application/octet-stream between processing body parts and other parts. (Arg-Required)

=item --attach-name [<name>]

This option sets the filename that will be included in the MIME part created for the next C<--attach> option.  If no argument is set for this option, it causes no filename information to be included for the next MIME part, even if Swaks could generate it from the local file name. (Arg-Optional)

=item -ah, --add-header <header>

This option allows headers to be added to the DATA.  If C<%NEW_HEADERS%> is present in the DATA it is replaced with the argument to this option.  If C<%NEW_HEADERS%> is not present, the argument is inserted between the first two consecutive newlines in the DATA (that is, it is inserted at the end of the existing headers).

The option can either be specified multiple times or a single time with multiple headers separated by a literal C<\n> string.  So, C<--add-header 'Foo: bar' --add-header 'Baz: foo'" and "--add-header 'Foo: bar\nBaz: foo'> end up adding the same two headers. (Arg-Required)

=item --header <header-and-data>, --h-<header> <data>

These options allow a way to change headers that already exist in the DATA.  C<--header 'Subject: foo'> and C<--h-Subject foo> are equivalent.  If the header does not already exist in the data then this argument behaves identically to C<--add-header>.  However, if the header already exists it is replaced with the one specified.  Negating the version of this option with the header name in the option (eg C<--no-header-Subject>) will remove all previously processed C<--header> options, not just the ones used for 'Subject'. Embedding the header name in the option via environment variable is not supported on Windows and will result in an error. (Arg-Required)

=item -g

This option is a direct alias to C<--data -> (read DATA from C<STDIN>).  It is totally secondary to C<--data>.  Any occurrence of C<--data> will cause C<-g> to be ignored.  This option cannot be negated with the C<no-> prefix.  This option is deprecated and will be removed in a future version of Swaks. (Arg-None, Deprecated)

=item --no-data-fixup, -ndf

This option forces Swaks to do no massaging of the DATA portion of the email.  This includes token replacement, From_ stripping, trailing-dot addition, C<--body>/attachment inclusion, and any header additions.  This option is only useful when used with C<--data>, since the internal default DATA portion uses tokens. (Arg-None)

=item --no-strip-from, -nsf

Don't strip the From_ line from the DATA portion, if present. (Arg-None)

=back

=head1 OUTPUT OPTIONS

Swaks provides a transcript of its transactions to its caller (C<STDOUT>/C<STDERR>) by default.  This transcript aims to be as faithful a representation as possible of the transaction though it does modify this output by adding informational prefixes to lines and by providing plaintext versions of TLS transactions

The "informational prefixes" are referred to as transaction hints.  These hints are initially composed of those marking lines that are output of Swaks itself, either informational or error messages, and those that indicate a line of data actually sent or received in a transaction.  This table indicates the hints and their meanings:

=over 4

=item C<===>

Indicates an informational line generated by Swaks.

=item C<***>

Indicates an error generated within Swaks.

=item C<S< >-E<gt>>

Indicates an expected line sent by Swaks to target server.

=item C<S< >~E<gt>>

Indicates a TLS-encrypted, expected line sent by Swaks to target server.

=item C<**E<gt>>

Indicates an unexpected line sent by Swaks to the target server.

=item C<*~E<gt>>

Indicates a TLS-encrypted, unexpected line sent by Swaks to target server.

=item C<S<  >E<gt>>

Indicates a raw chunk of text sent by Swaks to a target server (see C<--show-raw-text>).  There is no concept of "expected" or "unexpected" at this level.

=item C<E<lt>-S< >>

Indicates an expected line sent by target server to Swaks.

=item C<E<lt>~S< >>

Indicates a TLS-encrypted, expected line sent by target server to Swaks.

=item C<E<lt>**>

Indicates an unexpected line sent by target server to Swaks.

=item C<E<lt>~*>

Indicates a TLS-encrypted, unexpected line sent by target server to Swaks.

=item C<E<lt>S<  >>

Indicates a raw chunk of text received by Swaks from a target server (see C<--show-raw-text>).  There is no concept of "expected" or "unexpected" at this level.

=back

The following options control what and how output is displayed to the caller.

=over 4

=item -n, --suppress-data

Summarizes the DATA portion of the SMTP transaction instead of printing every line.  This option is very helpful, bordering on required, when using Swaks to send certain test emails.  Emails with attachments, for instance, will quickly overwhelm a terminal if the DATA is not suppressed. (Arg-None)

=item -stl, --show-time-lapse [i]

Display time lapse between send/receive pairs.  This option is most useful when L<Time::HiRes> is available, in which case the time lapse will be displayed in thousandths of a second.  If L<Time::HiRes> is unavailable or "i" is given as an argument the lapse will be displayed in integer seconds only. (Arg-Optional)


Don't display the transaction hint for informational transactions.  This is most useful when needing to copy some portion of the informational lines, for instance the certificate output from C<--tls-get-peer-cert>. (Arg-None)

=item -nih, --no-info-hints

=item -nsh, --no-send-hints

=item -nrh, --no-receive-hints

=item -nth, --no-hints

C<--no-info-hints>, C<--no-send-hints>, and C<--no-receive-hints> suppress the transaction hints from info, send, and receive lines, respectively.  This is often useful when copying some portion of the transaction for use elsewhere (for instance, C<--no-send-hints --hide-receive --hide-informational> is a useful way to get only the client-side commands for a given transaction and C<--no-info-hints --tls-get-peer-cert> for copying the peer certificate).  C<--no-hints> is identical to specifying C<--no-info-hints --no-send-hints --no-receive-hints>. (Arg-None)

=item -raw, --show-raw-text

This option will print a hex dump of raw data sent and received by Swaks.  Each hex dump is the contents of a single read or write on the network.  This should be identical to what is already being displayed (with the exception of the C<\r> characters being removed).  This option is useful in seeing details when servers are sending lots of data in single packets, or breaking up individual lines into multiple packets.  If you really need to go in depth in that area you're probably better with a packet sniffer, but this option is a good first step to seeing odd connection issues. (Arg-None)

=item --output, --output-file <file-path>

=item --output-file-stdout <file-path>

=item --output-file-stderr <file-path>

These options allow the user to send output to files instead of C<STDOUT>/C<STDERR>.  The first option sends both to the same file.  The arguments of C<&STDOUT> and C<&STDERR> are treated specially, referring to the "normal" file handles, so C<--output-file-stderr '&STDOUT'> would redirect C<STDERR> to C<STDOUT>.  These options are honored for all output except C<--help> and C<--version>. (Arg-Required)

=item -pp, --protect-prompt

Don't echo user input on prompts that are potentially sensitive (right now only authentication password).  Very specifically, any option which is marked 'Sensitive' and eventually prompts for an argument will do its best to mask that argument from being echoed.  See also C<--auth-hide-password>. (Arg-None)

=item -hr, --hide-receive

Don't display lines sent from the remote server being received by Swaks. (Arg-None)

=item -hs, --hide-send

Don't display lines being sent by Swaks to the remote server. (Arg-None)

=item -hi, --hide-informational

Don't display non-error informational lines from Swaks itself. (Arg-None)

=item -ha, --hide-all

Do not display any content to the terminal. (Arg-None)

=item -S, --silent [ 1 | 2 | 3 ]

Cause Swaks to be silent.  If no argument is given or if an argument of "1" is given, print no output unless/until an error occurs, after which all output is shown.  If an argument of "2" is given, only print errors.  If "3" is given, show no output ever.  C<--silent> affects most output but not all.  For instance, C<--help>, C<--version>, C<--dump>, and C<--dump-mail> are not affected.  For historical reasons, -S is not settable via environment variable on Windows, use SWAKS_OPT_silent instead. (Arg-Optional)

=item --support

Print capabilities and exit.  Certain features require non-standard Perl modules.  This option evaluates whether these modules are present and displays which functionality is available and which isn't, and which modules would need to be added to gain the missing functionality. (Arg-None)

=item --dump-mail

Cause Swaks to process all options to generate the message it would send, then print that message to C<STDOUT> instead of sending it.  This output is identical to the "data" section of C<--dump>, except without the trailing dot. (Arg-None)

=item --dump [<section>[,<section>[,...]]]

This option causes Swaks to print the results of option processing, immediately before mail would have been sent.  No mail will be sent when C<--dump> is used.  Note that C<--dump> is a pure self-diagnosis tool and no effort is made or will ever be made to mask passwords in the C<--dump> output. If a section is provided as an argument to this option, only the requested section will be shown.  Currently supported arguments are SUPPORT, APP, OUTPUT, TRANSPORT, PROTOCOL, XCLIENT, PROXY, TLS, AUTH, DATA, and ALL.  If no argument is provided, all sections are displayed (Arg-Optional)

=item --help

Display this help information and exit. (Arg-None)

=item --version

Display version information and exit. (Arg-None)

=back

=head1 DEPRECATIONS

The following features are deprecated and will be removed in a future version of Swaks

=over 4

=item use of IO::Socket and IO::Socket::INET6 modules

Will be removed no sooner than (February 1, 2025).

The primary method of sending over IPv4 and IPv6 sockets is implemented with the L<IO::Socket::IP> module.  For the time being there is still legacy support of the L<IO::Socket> and L<IO::Socket::INET6> modules which were previously used.  Please ensure L<IO::Socket::IP> is installed to ensure future functionality.

=back

=head1 PORTABILITY

=over 4

=item OPERATING SYSTEMS

This program was primarily intended for use on UNIX-like operating systems, and it should work on any reasonable version thereof.  It has been developed and tested on Solaris, Linux, and Mac OS X and is feature complete on all of these.

This program is known to demonstrate basic functionality on Windows using Strawberry Perl.  In all documentation, unless otherwise noted, "Windows" refers to running Swaks via CMD.exe, not WSL or cygwin.  It has not been fully tested, but known to work are basic SMTP functionality and the LOGIN, PLAIN, and CRAM-MD5 auth types.  Unknown is any TLS functionality and the NTLM/SPA and DIGEST-MD5 auth types.  Some functionality is known to be limited on Windows, including inability to embed header name in environment variables (see L</"CONFIGURATION ENVIRONMENT VARIABLES"> and C<--header>), inability to generate a local-timezone date string (see C<%DATE%> token under C<--data>), inability to use C<-S> option as an environment variable (see C<--silent>), and inability to have a "set but empty" value in an environment variable (see L</"CONFIGURATION ENVIRONMENT VARIABLES"> for workaround).

Because this program should work anywhere Perl works, I would appreciate knowing about any new operating systems you've thoroughly used Swaks on as well as any problems encountered on a new OS.

=item MAIL SERVERS

This program was almost exclusively developed against Exim mail servers.  It has been used casually by the author, though not thoroughly tested, with Sendmail, Smail, Exchange, Oracle Collaboration Suite, qpsmtpd, and Communigate.  Because all functionality in Swaks is based on known standards it should work with any fairly modern mail server.  If a problem is found, please alert the author at the address below.

=back

=head1 ENVIRONMENT VARIABLES

=over 4

=item LOGNAME

If Swaks must create a sender address, C<$LOGNAME> is used as the message local-part if it is set, and unless C<--force-getpwuid> is used.

=item SWAKS_HOME

Used when searching for a F<.swaksrc> configuration file.  See OPTION PROCESSING -> L</"CONFIGURATION FILES"> above.

=item SWAKS_OPT_*

Environment variable prefix used to specify Swaks options from environment variables.  See OPTION PROCESSING -> L</"CONFIGURATION ENVIRONMENT VARIABLES"> above.

=back

=head1 EXIT CODES

=over 4

=item Z<>0

no errors occurred

=item Z<>1

error parsing command line options

=item Z<>2

error connecting to remote server

=item Z<>3

unknown connection type

=item Z<>4

while running with connection type of "pipe", fatal problem writing to or reading from the child process

=item Z<>5

while running with connection type of "pipe", child process died unexpectedly.  This can mean that the program specified with C<--pipe> doesn't exist.

=item Z<>6

Connection closed unexpectedly.  If the close is detected in response to the 'QUIT' Swaks sends following an unexpected response, the error code for that unexpected response is used instead.  For instance, if a mail server returns a 550 response to a MAIL FROM: and then immediately closes the connection, Swaks detects that the connection is closed, but uses the more specific exit code 23 to detail the nature of the failure.  If instead the server return a 250 code and then immediately closes the connection, Swaks will use the exit code 6 because there is not a more specific exit code.

=item Z<>10

error in prerequisites (needed module not available)

=item Z<>21

error reading initial banner from server

=item Z<>22

error in HELO transaction

=item Z<>23

error in MAIL transaction

=item Z<>24

no RCPTs accepted

=item Z<>25

server returned error to DATA request

=item Z<>26

server did not accept mail following data

=item Z<>27

server returned error after normal-session quit request

=item Z<>28

error in AUTH transaction

=item Z<>29

error in TLS transaction

=item Z<>30

PRDR requested/required but not advertised

=item Z<>32

error in EHLO following TLS negotiation

=item Z<>33

error in XCLIENT transaction

=item Z<>34

error in EHLO following XCLIENT

=item Z<>35

error in PROXY option processing

=item Z<>36

error sending PROXY banner

=back

=head1 ABOUT THE NAME

The name "Swaks" is a (sort-of) acronym for "SWiss Army Knife SMTP".  It was chosen to be fairly distinct and pronounceable.  While "Swaks" is unique as the name of a software package, it has some other, non-software meanings.  Please send in other uses of "swak" or "swaks" for inclusion.

=over 4

=item "Sealed With A Kiss"

SWAK/SWAKs turns up occasionally on the internet with the meaning "with love".

=item bad / poor / ill (Afrikaans)

Seen in the headline "SA se bes en swaks gekledes in 2011", which was translated as "best and worst dressed" by native speakers.  Google Translate doesn't like "swaks gekledes", but it will translate "swak" as "poor" and "swak geklede" as "ill-dressed".

=back

=head1 LICENSE

This program is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 2 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA.

=head1 CONTACT INFORMATION

General contact, questions, patches, requests, <NAME_EMAIL>.

Change logs, this help, and the latest version are found at L<http://www.jetmore.org/john/code/swaks/>.

Swaks is crafted with love by John Jetmore from the cornfields of Indiana, United States of America.

=head1 NOTIFICATIONS

=over 4

=item Email

<EMAIL>

If you would like to be put on a list to receive notifications when a new version of Swaks is released, please send an email to this address.  There will not be a response to your email.

=item Website

L<http://www.jetmore.org/john/blog/c/swaks/>

=item RSS Feed

L<http://www.jetmore.org/john/blog/c/swaks/feed/>

=item Twitter

<http://twitter.com/SwaksSMTP>

=back

=cut
