App Info:
  X-Mailer = swaks v99999999.9 jetmore.org/john/code/swaks/
  Cmd Line = %SWAKS_COMMAND% --from '<EMAIL>' --to '<EMAIL>' --helo 'host1.nodns.test.swaks.net' --server 'ser.ver' --dump-as-body 'app,auth' --auth-user 'TEST_USER' --auth-password 'TEST_PASS' --auth-hide-password --dump 'app,auth,data'

Authentication Info:
  auth           = required
  username       = 'TEST_USER'
  password       = 'TEST_PASS'
  show plaintext = FALSE
  hide password  = PROVIDED_BUT_REMOVED
  allowed types  = CRAM-MD5, CRAM-SHA1, DIGEST-MD5, LOGIN, LOGIN-INITIAL, MSN, NTLM, PLAIN, SPA
  extras         = 
  type map       = CRAM-MD5 = CRAM-MD5
                   CRAM-SHA1 = CRAM-SHA1
                   DIGEST-MD5 = DIGEST-MD5
                   LOGIN = LOGIN, LOGIN-INITIAL
                   NTLM = NTLM, SPA, MSN
                   PLAIN = PLAIN

DATA Info:
  data = <<.
Date: Wed, 03 Nov 1999 11:24:29 -0500
To: <EMAIL>
From: <EMAIL>
Subject: test Wed, 03 Nov 1999 11:24:29 -0500
Message-Id: <19991103112429.047942@localhost>
X-Mailer: swaks v99999999.9 jetmore.org/john/code/swaks/

App Info:
  X-Mailer = swaks v99999999.9 jetmore.org/john/code/swaks/
  Cmd Line = %SWAKS_COMMAND% --from '<EMAIL>' --to '<EMAIL>' --helo 'host1.nodns.test.swaks.net' --server 'ser.ver' --dump-as-body 'app,auth' --auth-user 'TEST_USER' --auth-password 'PROVIDED_BUT_REMOVED' --auth-hide-password --dump 'app,auth,data'

Authentication Info:
  auth           = required
  username       = 'TEST_USER'
  password       = 'PROVIDED_BUT_REMOVED'
  show plaintext = FALSE
  hide password  = PROVIDED_BUT_REMOVED
  allowed types  = CRAM-MD5, CRAM-SHA1, DIGEST-MD5, LOGIN, LOGIN-INITIAL, MSN, NTLM, PLAIN, SPA
  extras         = 
  type map       = CRAM-MD5 = CRAM-MD5
                   CRAM-SHA1 = CRAM-SHA1
                   DIGEST-MD5 = DIGEST-MD5
                   LOGIN = LOGIN, LOGIN-INITIAL
                   NTLM = NTLM, SPA, MSN
                   PLAIN = PLAIN



.
