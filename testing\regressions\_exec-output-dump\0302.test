auto: REMOVE_FILE,CREATE_FILE,<PERSON>UN<PERSON>,COMPARE_FILE %TESTID%.stdout %TESTID%.stderr %TESTID%.exits

test action: CMD_CAPTURE %SWAKS% --dump tls --to <EMAIL> --server ser.ver --helo host1.nodns.test.swaks.net --from <EMAIL> --tls --tls-protocol TLSv1_1 --tls-cipher TEST_CIPHER --tls-verify --tls-ca-path %TESTDIR%/%TESTID%.test --tls-cert /LOCAL/CERT --tls-key /LOCAL/KEY --tls-chain /LOCAL/CHAIN --tls-get-peer-cert /PEER/CERT --tls-get-peer-chain /PEER/CHAIN

