auto: REMOVE_FILE,CREATE_FILE,<PERSON>UN<PERSON>,COMPARE_FILE %TESTID%.stdout %TESTID%.stderr %TESTID%.exits

test action: CMD_CAPTURE %SWAKS% --dump --to <EMAIL> --server ser.ver --helo host1.nodns.test.swaks.net --from <EMAIL> --quit-after 'mail' --auth-user 'user' --auth-password 'pass' --tls --hide-send --dump --xclient-addr '***********' --xclient-optional --proxy-version '1' --proxy-family 'TCP4' --proxy-source '***********' --proxy-source-port '111' --proxy-dest '*******' --proxy-dest-port '26'
