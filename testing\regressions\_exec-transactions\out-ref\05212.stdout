=== Trying pipe to %TEST_SERVER% --silent --domain pipe   part-0000-connect-standard.txt   part-0101-ehlo-all.txt   part-0200-starttls-basic.txt   part-0101-ehlo-all.txt   ...
=== Connected to %TEST_SERVER% --silent --domain pipe   part-0000-connect-standard.txt   part-0101-ehlo-all.txt   part-0200-starttls-basic.txt   part-0101-ehlo-all.txt   .
<-  220 SERVER ESMTP ready
 -> EHLO hserver
<-  250-SERVER Hello Server [*******]
<-  250-STARTTLS
<-  250-PIPELINING
<-  250-XCLIENT ADDR NAME PORT PROTO DESTADDR DESTPORT HELO LOGIN REVERSE_NAME
<-  250-PRDR
<-  250-AUTH CRAM-MD5
<-  250-AUTH CRAM-SHA1
<-  250-AUTH PLAIN
<-  250-AUTH LOGIN
<-  250-AUTH NTLM
<-  250-AUTH DIGEST-MD5
<-  250-AUTH=login
<-  250 HELP
 -> STARTTLS
<-  220 TLS go ahead
=== TLS started with cipher VERSION:CIPHER:BITS
=== TLS client certificate not requested and not sent
=== TLS no client certificate set
=== TLS peer[0]   subject=[/C=US/ST=Indiana/O=Swaks Development (node.example.com, with-SAN)/CN=node.example.com/emailAddress=<EMAIL>]
===               commonName=[node.example.com], subjectAltName=[DNS:node.example.com] notAfter=[2033-09-11T14:50:10Z]
=== TLS peer certificate failed CA verification (unable to get local issuer certificate), failed host verification (no host string available to verify)
 ~> EHLO hserver
=== Dropping connection
