auto: REMOVE_FILE,CREATE_FILE,<PERSON>UN<PERSON>,COMPARE_FILE %TESTID%.stdout %TESTID%.stderr %TESTID%.exits

test action: CMD_CAPTURE %SWAKS% --dump auth --to <EMAIL> --server ser.ver --helo host1.nodns.test.swaks.net --from <EMAIL> --auth LOGIN,PLAIN --auth-user AUTHUSER --auth-password AUTHPASS --auth-hide-password CUSTOM_HIDE_STRING --auth-plaintext --auth-map AUTHTYPE=CRAM-MD5,TESTTYPE2=TESTTYPE2,AUTHTYPE=LOGIN --auth-extra dmd5-host=dmd5.nodns.test.swaks.net,realm=realm.nodns.test.swaks.net
