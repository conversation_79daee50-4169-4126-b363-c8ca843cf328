#!/usr/bin/env bash

export SWAKS_TEST_SWAKS=../../swaks
export SWAKS_TEST_EDITOR='/Applications/Sublime Text.app/Contents/SharedSupport/bin/subl'
export SWAKS_TEST_SERVER=../server/smtp-server.pl


# Either or both of these can be really convenient when you have a ton of small changes to accept.
# Setting SWAKS_TEST_PAGER to cat means you don't have to quit out of a pager when viewing the diff
export SWAKS_TEST_PAGER=cat
# Setting SWAKS_TEST_AUTOCAT to 1 means that everytime a test fails, the diff is auto-catted for review
export SWAKS_TEST_AUTOCAT=1

exec $*
