=== Trying pipe to %TEST_SERVER% --silent --domain pipe   part-0000-connect-standard.txt   part-0100-ehlo-basic.txt   part-1000-mail-basic.txt   part-1100-rcpt-basic-accept.txt   part-2500-data-accept-basic.txt   part-3000-shutdown-accept.txt   ...
=== Connected to %TEST_SERVER% --silent --domain pipe   part-0000-connect-standard.txt   part-0100-ehlo-basic.txt   part-1000-mail-basic.txt   part-1100-rcpt-basic-accept.txt   part-2500-data-accept-basic.txt   part-3000-shutdown-accept.txt   .
<-  220 SERVER ESMTP ready
 -> EHLO hserver
<-  250-SERVER Hello Server [*******]
<-  250 HELP
 -> MAIL FROM:<<EMAIL>>
<-  250 Accepted
 -> RCPT TO:<<EMAIL>>
<-  250 Accepted
 -> DATA
<-  354 Enter message, ending with "." on a line by itself
 -> 11 lines sent
<-  250 OK id=fakeemail
 -> QUIT
<-  221 SERVER closing connection
=== Connection closed with child process.
